<?php
/**
 * 数据库配置文件
 * 
 * 定义应用程序使用的数据库连接信息
 */

// 确保ROOT_PATH常量已定义
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}

return [
    /*
    |--------------------------------------------------------------------------
    | 默认数据库连接
    |--------------------------------------------------------------------------
    |
    | 这里指定默认使用的数据库连接名称
    |
    */
    'default' => 'sqlite',
    
    /*
    |--------------------------------------------------------------------------
    | 数据库连接配置
    |--------------------------------------------------------------------------
    |
    | 这里定义所有可用的数据库连接配置
    | 支持MySQL和SQLite3两种数据库类型
    |
    */
    'connections' => [
        /*
        |--------------------------------------------------------------------------
        | SQLite3 数据库连接
        |--------------------------------------------------------------------------
        */
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => ROOT_PATH . '/database/database.sqlite',
            'prefix' => '',
        ],
        
        /*
        |--------------------------------------------------------------------------
        | MySQL 数据库连接
        |--------------------------------------------------------------------------
        */
        'mysql' => [
            'driver' => 'mysql',
            'host' => 'localhost',
            'port' => 3306,
            'database' => 'aiphp_app',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
        ],
    ],
];