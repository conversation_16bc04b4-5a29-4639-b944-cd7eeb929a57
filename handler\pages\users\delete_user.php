<?php
/**
 * 用户删除接口
 * 
 * 处理前端发送的用户删除请求
 * 接收用户ID，从数据库中删除对应的用户记录
 */

// 设置返回JSON格式
header('Content-Type: application/json');

// 使用RedBeanPHP ORM
use Core\OtherLibrary\RedBean\RedBeanFacade as R;
use Core\OwnLibrary\Security\CsrfTokenManager;

// 实例化CSRF令牌管理器
$csrfManager = new CsrfTokenManager();

// 初始化响应数组
$response = [
    'success' => false,
    'message' => ''
];

try {
    // 检查请求方法是否为POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法错误，只支持POST请求');
    }
    
    // 验证CSRF令牌
    if (!$csrfManager->validateRequestToken()) {
        throw new Exception('CSRF验证失败，请刷新页面后重试');
    }
    
    // 获取请求数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证参数
    if (!isset($data['id'])) {
        throw new Exception('参数不完整，缺少用户ID');
    }
    
    // 验证用户ID
    $userId = (int)$data['id'];
    
    if ($userId <= 0) {
        throw new Exception('用户ID无效');
    }
    
    // 检查用户是否存在
    $user = R::load('users', $userId);
    
    if (!$user->id) {
        throw new Exception('未找到指定用户');
    }
    
    // 删除用户
    R::trash($user);
    
    $response['success'] = true;
    $response['message'] = '用户删除成功';
    // 在响应中包含新生成的CSRF令牌
    $response['csrf_token'] = $csrfManager->getToken();
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// 返回JSON响应
echo json_encode($response);
exit;