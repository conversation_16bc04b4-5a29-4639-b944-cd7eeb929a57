<?php
/**
 * users表数据检查脚本
 * 
 * 用于检查users表中的数据
 */

// 定义所有必需的路径常量
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('HANDLER_PATH', ROOT_PATH . '/handler');
define('PAGES_PATH', HANDLER_PATH . '/pages');
define('LAYOUTS_PATH', HANDLER_PATH . '/layouts');
define('OWN_LIBRARY_PATH', CORE_PATH . '/own-library');
define('OTHER_LIBRARY_PATH', CORE_PATH . '/other-library');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STATIC_PATH', PUBLIC_PATH . '/static');

// 注册自动加载函数
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

// 使用自动加载引入RedBeanPHP类和门面类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

echo "users表数据检查...\n";

try {
    // 初始化RedBeanPHP
    echo "\n=== 初始化RedBeanPHP ===\n";
    R::initialize();
    echo "RedBeanPHP初始化成功\n";
    
    // 检查数据库连接
    echo "\n=== 检查数据库连接 ===\n";
    $test = \R::testConnection();
    if ($test) {
        echo "数据库连接成功\n";
    } else {
        echo "数据库连接失败\n";
        exit(1);
    }
    
    // 获取users表中的所有用户
    echo "\n=== 获取users表中的所有用户 ===\n";
    $users = R::findAll('users', 'ORDER BY id ASC');
    echo "找到 " . count($users) . " 个用户\n";
    
    // 显示用户数据
    if (!empty($users)) {
        echo "\n用户列表:\n";
        foreach ($users as $user) {
            echo "ID: " . $user->id . ", 用户名: " . $user->username . ", 邮箱: " . $user->email . ", 创建时间: " . $user->created_at . "\n";
        }
    } else {
        echo "users表为空\n";
    }
    
    echo "\n检查完成。\n";
    
} catch (Exception $e) {
    echo "检查出错: " . $e->getMessage() . "\n";
    echo "错误跟踪: " . $e->getTraceAsString() . "\n";
}