/**
 * 用户详情页面JavaScript
 * 处理页面交互逻辑
 */

// 页面加载完成后执行
$(document).ready(function() {
    
    // 初始化页面功能
    initUserDetailPage();
    
    // 添加CSRF令牌到AJAX请求头
    $.ajaxSetup({
        headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
        }
    });
});

/**
 * 初始化用户详情页面
 */
function initUserDetailPage() {
    addEditButtonListener();
    addDeleteButtonListener();
}

/**
 * 添加编辑按钮点击事件监听器
 */
function addEditButtonListener() {
    const editButton = document.getElementById('edit-button');
    if (editButton) {
        editButton.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            // 这里可以添加编辑功能的实现
            // 例如打开编辑模态框或跳转到编辑页面
            console.log('编辑用户ID:', userId);
            alert('编辑功能即将实现');
        });
    }
}

/**
 * 添加删除按钮点击事件监听器
 */
function addDeleteButtonListener() {
    const deleteButton = document.getElementById('delete-button');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            
            // 确认删除
            if (confirm('确定要删除这个用户吗？此操作不可撤销！')) {
                // 这里可以添加删除功能的实现
                // 例如发送AJAX请求到服务器删除用户
                console.log('删除用户ID:', userId);
                alert('删除功能即将实现');
                
                // 模拟删除成功后返回用户列表
                // window.location.href = '/users';
            }
        });
    }
}

/**
 * 处理AJAX请求错误
 */
function handleAjaxError(jqXHR, textStatus, errorThrown) {
    console.error('AJAX请求错误:', textStatus, errorThrown);
    
    // 检查响应是否包含CSRF验证失败的错误
    if (jqXHR.status === 403 && jqXHR.responseText.includes('CSRF验证失败')) {
        alert('CSRF验证失败，请刷新页面后重试');
        window.location.reload();
        return;
    }
    
    // 显示通用错误消息
    alert('操作失败: ' + (errorThrown || '未知错误'));
}

/**
 * 更新CSRF令牌
 */
function updateCsrfToken(newToken) {
    if (newToken && typeof newToken === 'string') {
        const metaElement = document.querySelector('meta[name="csrf-token"]');
        if (metaElement) {
            metaElement.setAttribute('content', newToken);
        }
        
        // 更新AJAX请求头中的令牌
        $.ajaxSetup({
            headers: {
                'X-CSRF-Token': newToken
            }
        });
    }
}