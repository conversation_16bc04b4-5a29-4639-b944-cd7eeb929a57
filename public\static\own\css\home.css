/* AiPHP框架首页样式 */

/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, #2d6a4f 0%, #40916c 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-desc {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background-color: white;
    color: #2d6a4f;
}

.btn-primary:hover {
    background-color: #f8fff8;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* 特性区域样式 */
.features-section {
    padding: 4rem 0;
    background-color: #f8fff8;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #2d6a4f;
}

.section-header p {
    color: #555;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-item {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 1.5rem;
    text-align: center;
}

.feature-item h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #2d6a4f;
    text-align: center;
}

.feature-item p {
    color: #555;
    text-align: center;
    line-height: 1.6;
}

/* 优势区域样式 */
.advantages-section {
    padding: 4rem 0;
    background-color: white;
}

.advantages-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.advantage-text, .advantage-image {
    flex: 1;
    min-width: 300px;
}

.advantage-text h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #2d6a4f;
}

.advantage-text p {
    margin-bottom: 1.5rem;
    color: #555;
    line-height: 1.6;
}

.advantage-text ul {
    list-style: none;
    padding: 0;
}

.advantage-text li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    color: #555;
}

.advantage-text li:before {
    content: '✓';
    color: #40916c;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.advantage-image {
    text-align: center;
}

/* 流程区域样式 */
.process-section {
    padding: 4rem 0;
    background-color: #f8fff8;
}

.process-steps {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.step-item {
    flex: 1;
    min-width: 250px;
    text-align: center;
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
}

.step-item:not(:last-child):after {
    content: '→';
    position: absolute;
    right: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: #40916c;
    display: none;
}

@media (min-width: 992px) {
    .step-item:not(:last-child):after {
        display: block;
    }
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: #40916c;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
}

.step-item h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: #2d6a4f;
}

.step-item p {
    color: #555;
    line-height: 1.6;
}

/* 号召性区域样式 */
.cta-section {
    background: linear-gradient(135deg, #2d6a4f 0%, #40916c 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-desc {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .advantage-text h3 {
        font-size: 1.5rem;
    }
    
    .cta-content h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .features-grid,
    .process-steps {
        grid-template-columns: 1fr;
    }
    
    .btn-primary, .btn-secondary {
        width: 100%;
        text-align: center;
    }
}