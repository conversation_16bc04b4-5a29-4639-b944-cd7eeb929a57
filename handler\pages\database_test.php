<?php
// 数据库测试页面

// 布局变量
$layout="green_layout";

// 设置页面特定变量
$pageTitle = '数据库测试 - AiPHP框架';
$pageDescription = '显示用户数据';
$pageKeywords = 'AiPHP,数据库,用户';

// 如果使用布局，需要引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/database_test.css'];
$additionalJS = ['/static/own/js/database_test.js'];

// 使用数据库类
use Core\OwnLibrary\Database\Database;

// 获取分页参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page); // 确保页码至少为1
$limit = 10; // 每页显示10条记录
$offset = ($page - 1) * $limit;

// 用户数据和分页信息
$users = [];
$totalUsers = 0;
$totalPages = 1;

try {
    // 创建数据库实例
    $db = new Database();
    
    // 获取总记录数
    $countResult = $db->query('SELECT COUNT(*) as count FROM users');
    $totalUsers = $countResult[0]['count'] ?? 0;
    $totalPages = ceil($totalUsers / $limit);
    
    // 查询当前页的用户数据
    $users = $db->table('users')->orderBy('id', 'ASC')->limit($limit, $offset)->get();
    
} catch (Exception $e) {
    // 如果出现错误，显示空的用户列表
    $users = [];
}
?>
<div class="database-test-page">
    <div class="user-data">
        <h2>用户数据</h2>
        <?php if (!empty($users)): ?>
        <table class="user-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>创建日期</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                    <td><?php echo htmlspecialchars($user['nickname'] ?? $user['username']); ?></td>
                    <td><?php echo htmlspecialchars($user['created_at'] ?? '未知'); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- 分页导航 -->
        <div class="pagination">
            <p>总共 <?php echo $totalUsers; ?> 条记录，共 <?php echo $totalPages; ?> 页，当前第 <?php echo $page; ?> 页</p>
            <div class="pagination-links">
                <?php if ($page > 1): ?>
                    <a href="?page=1">&laquo; 首页</a>
                    <a href="?page=<?php echo $page - 1; ?>">&lt; 上一页</a>
                <?php endif; ?>
                
                <?php
                // 显示页码链接
                $startPage = max(1, $page - 2);
                $endPage = min($totalPages, $page + 2);
                
                for ($i = $startPage; $i <= $endPage; $i++):
                ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">下一页 &gt;</a>
                    <a href="?page=<?php echo $totalPages; ?>">末页 &raquo;</a>
                <?php endif; ?>
            </div>
        </div>
        <?php else: ?>
        <p class="no-data">暂无用户数据</p>
        <?php endif; ?>
    </div>
    
    <div class="page-info">
        <h2>页面说明</h2>
        <p>本页面用于显示数据库中的用户数据，只显示ID、用户名和创建日期三个字段。</p>
        <p>支持分页功能，每页显示10条记录。</p>
    </div>
</div>