{"mcpServers": {"mysql": {"command": "uv", "args": ["--directory", "path/to/mysql_mcp_server", "run", "mysql_mcp_server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "root", "MYSQL_DATABASE": ""}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/path/to/other/allowed/dir"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}