# AiPHP 核心类创建提示词指南

## 概述
本指南详细说明了在AiPHP框架中创建核心类的规范和最佳实践，确保核心类的一致性、可维护性和可用性。

## 核心类命名规范
- 文件夹命名：使用小驼峰命名法 (camelCase)，例如：pageLoader、userManager
- 类名命名：使用大驼峰命名法 (PascalCase)，例如：PageLoader、UserManager
- 文件名命名：与类名保持一致，例如：PageLoader.php

## 核心类存放位置
- 所有核心类必须放置在项目根目录下的`core/own-library/`文件夹中
- 每个核心类应有独立的文件夹，路径格式为：`core/own-library/[小驼峰命名的文件夹]/[大驼峰命名的类文件].php`
- 例如：`core/own-library/pageLoader/PageLoader.php`

## 核心类内容规范
- 必须使用适当的命名空间，命名空间应与文件夹结构相对应
- 类文件必须包含详细的中文注释
- 注释应包含类的功能说明、属性说明和方法说明
- 主要目的是方便AI理解代码的意图和实现
- 使用PHP 8.0及以上版本特性
- 严格类型声明
- 遵循PSR-12编码规范

## 核心类结构建议
核心类建议包含以下结构：

1. **命名空间声明**：与文件夹路径相对应
2. **类注释**：详细说明类的功能和用途
3. **属性声明**：包含类型声明和详细注释
4. **构造函数**：初始化类的属性
5. **公共方法**：提供类的主要功能接口
6. **私有方法**：处理内部逻辑

**文档要求**：建立了核心类，就要写它的API文档，文档放到`docs`文件夹里，名称和类同名后面带有`_xjke`，例如`Router_xjke.md`。

## 核心类注释规范
- 类必须有文档注释，包含功能说明
- 所有公共方法必须有文档注释，包含参数和返回值说明
- 复杂逻辑需要行内注释
- 使用简洁明了的中文注释

## 核心类更新维护规范
- 当功能需求发生变化时，应及时更新相应的核心类
- 更新内容应与实际项目需求保持一致
- 新增功能时，应创建对应的核心类
- 定期审查现有核心类，确保其时效性和准确性
- 必须更新对应的核心类的API文档，放在`docs`目录下
- 文档文件名应与类名保持一致，后面带有`_xjke`，文档类型为`.md`，例如`Router_xjke.md`
- 如果没有对应的API文档，则必须根据核心类内容建立
- 文档中必须包含详细的使用说明、API参数说明和返回值说明

## 核心类使用规范
- 在使用核心类前，应确保自动加载器正确配置
- 严格按照核心类的接口规范使用
- 如有疑问或发现问题，应及时反馈并更新核心类

## 核心类测试规范
- 每个核心类都应编写对应的测试文件
- 测试文件必须放置在项目根目录下的`tests`文件夹中
- 测试文件命名规则：在类名后添加`Test`，使用大驼峰命名法
  - 例如：`Router.php`的测试文件名为`RouterTest.php`
- 执行测试时，终端命令不允许使用`&&`连接符
- 测试执行流程：
  1. 先查看当前终端的目录
  2. 如果当前目录与项目根目录同名
  3. 直接执行`php tests/RouterTest.php`命令运行测试文件
  4. 否则，使用相对路径执行测试文件
- 关于测试的具体规则，可以参考`prompts\test_guidelines.md`文件，测试规范以`prompts\test_guidelines.md`中的规则为主