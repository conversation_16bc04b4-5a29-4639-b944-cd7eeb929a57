<?php
/**
 * 测试修复后的分页功能
 * 
 * 验证删除调试代码后是否解决了500错误问题
 */

// 定义必要的常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// 引入自动加载器
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary', CORE_PATH . '/other-library');
// 更新命名空间映射以匹配实际目录结构
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

// 引入必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

try {
    // 获取总记录数
    $totalUsers = R::count('users');
    $totalPages = ceil($totalUsers / 10);
    
    echo "数据库中用户总数: " . $totalUsers . "\n";
    echo "总页数: " . $totalPages . "\n\n";
    
    // 测试所有页面，包括最后一页
    for ($page = 1; $page <= $totalPages; $page++) {
        $limit = 10;
        $offset = ($page - 1) * $limit;
        
        echo "测试第 {$page} 页 (OFFSET = {$offset}):\n";
        try {
            $users = R::find('users', ' ORDER BY id ASC LIMIT ? OFFSET ?', [$limit, $offset]);
            echo "  成功获取 " . count($users) . " 条记录\n";
            
            // 显示前几条记录的ID
            $ids = [];
            foreach ($users as $user) {
                $ids[] = $user->id;
            }
            echo "  用户ID: " . implode(', ', array_slice($ids, 0, 5)) . (count($ids) > 5 ? '...' : '') . "\n\n";
        } catch (Exception $e) {
            echo "  错误: " . $e->getMessage() . "\n\n";
        }
    }
    
    echo "测试完成！\n";
    
} catch (Exception $e) {
    echo "数据库操作失败: " . $e->getMessage() . "\n";
}
?>