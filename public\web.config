<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <!-- 允许static目录中的静态文件直接访问 -->
                <rule name="Allow Static Files" stopProcessing="true">
                    <match url="^static/(.*)$" ignoreCase="true" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" />
                    </conditions>
                    <action type="None" />
                </rule>
                
                <!-- 处理测试500错误的脚本 -->
                <rule name="Test 500 Error" stopProcessing="true">
                    <match url="^test_500\.php$" ignoreCase="true" />
                    <action type="Rewrite" url="test_500.php" />
                </rule>
                
                <!-- 处理不支持的HTTP方法，重定向到/static/index.html -->
                <rule name="Handle Unsupported HTTP Methods" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAny">
                        <add input="{REQUEST_METHOD}" pattern="^PUT$" />
                        <add input="{REQUEST_METHOD}" pattern="^DELETE$" />
                        <add input="{REQUEST_METHOD}" pattern="^PATCH$" />
                        <add input="{REQUEST_METHOD}" pattern="^OPTIONS$" />
                        <add input="{REQUEST_METHOD}" pattern="^HEAD$" />
                    </conditions>
                    <action type="Redirect" url="/static/index.html?method={REQUEST_METHOD}" redirectType="Permanent" />
                </rule>
                
                <!-- 首页路由 -->
                <rule name="Index Route" stopProcessing="true">
                    <match url="^$" ignoreCase="false" />
                    <action type="Rewrite" url="index.php" />
                </rule>
                
                <!-- 支持中文参数的路由规则 -->
                <rule name="Chinese Parameter Route" stopProcessing="true">
                    <match url="^(.*)$" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php?url={R:1}" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- 允许双倍转义 -->
        <security>
            <requestFiltering allowDoubleEscaping="true">
                <fileExtensions allowUnlisted="true" />
            </requestFiltering>
        </security>
        
        <!-- 处理错误页面 - 启用详细错误信息 -->
        <httpErrors errorMode="Detailed" existingResponse="PassThrough">
            <!-- 移除默认的错误页面，显示详细错误信息 -->
            <clear />
        </httpErrors>

        <!-- 启用详细的错误信息显示 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="DENY" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>

        <!-- 启用失败请求跟踪 -->
        <tracing>
            <traceFailedRequests>
                <add path="*">
                    <traceAreas>
                        <add provider="ASP" verbosity="Verbose" />
                        <add provider="ASPNET" areas="Infrastructure,Module,Page,AppServices" verbosity="Verbose" />
                        <add provider="ISAPI Extension" verbosity="Verbose" />
                        <add provider="WWW Server" areas="Authentication,Security,Filter,StaticFile,CGI,Compression,Cache,RequestNotifications,Module,FastCGI,WebSocket" verbosity="Verbose" />
                    </traceAreas>
                    <failureDefinitions statusCodes="400-600" />
                </add>
            </traceFailedRequests>
        </tracing>
    </system.webServer>
    
    <!-- 配置ASP.NET错误处理 -->
    <system.web>
        <customErrors mode="Off" />
        <compilation debug="true" />
    </system.web>
</configuration>