<?php
/**
 * 使用RedBeanPHP的删除用户接口
 */

use Core\OtherLibrary\RedBean\RedBeanFacade as R;

header('Content-Type: application/json');

try {
    // 验证CSRF令牌
    $token = $_SERVER['HTTP_X_REQUESTED_WITH'] ?? '';
    if ($token !== 'XMLHttpRequest') {
        throw new Exception('无效的请求类型');
    }
    
    // 获取用户ID
    $uri = $_SERVER['REQUEST_URI'];
    $userId = basename($uri);
    
    if (empty($userId)) {
        throw new Exception('参数不完整，缺少用户ID');
    }
    
    // 检查用户是否存在（从users表而不是user表）
    $user = R::load('users', $userId);
    if (!$user->id) {
        throw new Exception('用户不存在');
    }
    
    // 删除用户
    R::trash($user);
    
    echo json_encode([
        'success' => true,
        'message' => '用户删除成功'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}