// 数据库测试页面的JavaScript代码

document.addEventListener('DOMContentLoaded', function() {
    // 添加页面加载完成后的处理逻辑
    console.log('数据库测试页面加载完成');
    
    // 测试连接按钮事件
    const testConnectionBtn = document.getElementById('test-connection');
    if (testConnectionBtn) {
        testConnectionBtn.addEventListener('click', function() {
            const statusElement = document.getElementById('connection-status');
            statusElement.textContent = '测试中...';
            statusElement.className = 'pending';
            
            // 模拟异步测试过程
            setTimeout(function() {
                // 模拟测试结果（实际应用中会发送请求到服务器）
                const isSuccess = Math.random() > 0.2; // 80% 成功率
                
                if (isSuccess) {
                    statusElement.textContent = '连接成功';
                    statusElement.className = 'success';
                } else {
                    statusElement.textContent = '连接失败';
                    statusElement.className = 'error';
                }
            }, 1500);
        });
    }
    
    // 测试查询按钮事件
    const testQueryBtn = document.getElementById('test-query');
    if (testQueryBtn) {
        testQueryBtn.addEventListener('click', function() {
            const resultElement = document.getElementById('query-result');
            resultElement.textContent = '查询中...';
            resultElement.className = 'pending';
            
            // 模拟异步查询过程
            setTimeout(function() {
                // 模拟查询结果（实际应用中会发送请求到服务器）
                const isSuccess = Math.random() > 0.1; // 90% 成功率
                
                if (isSuccess) {
                    resultElement.textContent = '查询成功，返回10条记录';
                    resultElement.className = 'success';
                } else {
                    resultElement.textContent = '查询失败';
                    resultElement.className = 'error';
                }
            }, 1500);
        });
    }
    
    // 测试插入按钮事件
    const testInsertBtn = document.getElementById('test-insert');
    if (testInsertBtn) {
        testInsertBtn.addEventListener('click', function() {
            const resultElement = document.getElementById('insert-result');
            resultElement.textContent = '插入中...';
            resultElement.className = 'pending';
            
            // 模拟异步插入过程
            setTimeout(function() {
                // 模拟插入结果（实际应用中会发送请求到服务器）
                const isSuccess = Math.random() > 0.15; // 85% 成功率
                
                if (isSuccess) {
                    resultElement.textContent = '插入成功，ID: ' + Math.floor(Math.random() * 1000);
                    resultElement.className = 'success';
                } else {
                    resultElement.textContent = '插入失败';
                    resultElement.className = 'error';
                }
            }, 1500);
        });
    }
    
    // 为页面标题添加动画效果
    const databaseTestPage = document.querySelector('.database-test-page');
    if (databaseTestPage) {
        const pageTitle = databaseTestPage.querySelector('h1');
        if (pageTitle) {
            pageTitle.style.opacity = '0';
            pageTitle.style.transform = 'translateY(-20px)';
            pageTitle.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            
            setTimeout(() => {
                pageTitle.style.opacity = '1';
                pageTitle.style.transform = 'translateY(0)';
            }, 100);
        }
    }
});