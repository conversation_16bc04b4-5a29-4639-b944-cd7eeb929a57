<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        spl_autoload_register(array('ComposerAutoloaderInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
