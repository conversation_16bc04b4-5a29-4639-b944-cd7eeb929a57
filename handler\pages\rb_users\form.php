<?php
/**
 * 使用RedBeanPHP的用户表单页面（添加/编辑）
 */
// 布局变量
$layout="green_layout";
// 设置页面标题和描述
$pageTitle = ($userId ? '编辑用户' : '添加用户') . ' - RedBeanPHP版本';
$pageDescription = '使用RedBeanPHP ORM的用户表单页面';
$pageKeywords = '用户表单, RedBeanPHP, ORM';

// 添加页面特定的CSS和JS文件
$additionalCSS = ['/static/own/css/rb_users/form.css'];
$additionalJS = ['/static/own/js/rb_users/form.js'];

// 获取用户ID（如果是编辑）
$userId = $_GET['id'] ?? null;
$user = null;

if ($userId) {
    try {
        // 获取用户信息（从users表而不是user表）
        $user = R::load('users', $userId);
        if (!$user->id) {
            header('Location: /rb_users');
            exit;
        }
    } catch (Exception $e) {
        $error = "获取用户信息失败: " . $e->getMessage();
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = $_POST['username'] ?? '';
        $email = $_POST['email'] ?? '';
        
        if (empty($username)) {
            throw new Exception('用户名不能为空');
        }
        
        if (empty($email)) {
            throw new Exception('邮箱不能为空');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('邮箱格式不正确');
        }
        
        if ($userId) {
            // 编辑用户（从users表而不是user表）
            $user = R::load('users', $userId);
            if (!$user->id) {
                throw new Exception('用户不存在');
            }
        } else {
            // 创建新用户（使用users表而不是user表）
            $user = R::dispense('users');
            $user->created_at = date('Y-m-d H:i:s');
        }
        
        $user->username = $username;
        $user->email = $email;
        
        $id = R::store($user);
        
        // 重定向到详情页面
        header('Location: /rb_users/detail/' . $id);
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?php echo $userId ? '编辑用户' : '添加用户'; ?></h3>
                    <div class="card-tools">
                        <a href="/rb_users" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <form method="POST" action="/rb_users/save">
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($user->username ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">邮箱</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($user->email ?? ''); ?>" required>
                        </div>
                        
                        <?php if ($userId): ?>
                            <input type="hidden" name="id" value="<?php echo htmlspecialchars($userId); ?>">
                        <?php endif; ?>
                        
                        <button type="submit" class="btn btn-primary"><?php echo $userId ? '更新' : '添加'; ?></button>
                        <a href="/rb_users" class="btn btn-secondary">取消</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>