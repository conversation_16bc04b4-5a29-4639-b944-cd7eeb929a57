<?php
/**
 * 使用RedBeanPHP的用户管理页面
 */
// 布局变量
$layout="green_layout";
// 设置页面标题和描述
$pageTitle = '用户管理 - RedBeanPHP版本';
$pageDescription = '使用RedBeanPHP ORM的用户管理页面';
$pageKeywords = '用户管理, RedBeanPHP, ORM';

// 添加页面特定的CSS和JS文件
$additionalCSS = ['/static/own/css/rb_users/index.css'];
$additionalJS = ['/static/own/js/rb_users/index.js'];

// 获取所有用户
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

try {
    // 启用缓存功能
    R::enableCache(true);
    
    // 启用调试模式查看缓存效果（可选，生产环境建议关闭）
    // $logger = R::debug(true, 2);
    
    // 使用缓存查询获取所有用户
    $users = R::findWithCache('users', 'ORDER BY id ASC');
    
    // 获取缓存统计信息（可选，用于调试）
    $cacheStats = R::getCacheStats();
    
} catch (Exception $e) {
    $error = "获取用户列表失败: " . $e->getMessage();
    $users = [];
    $cacheStats = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">用户管理 (RedBeanPHP版本)</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-info btn-sm" id="refreshCache" title="刷新缓存">
                            <i class="fas fa-sync-alt"></i> 刷新缓存
                        </button>
                        <a href="/rb_users/create" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加用户
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <!-- 缓存信息显示区域 -->
                    <?php if (!empty($cacheStats)): ?>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-database"></i> 缓存状态</h6>
                            <small>
                                缓存状态: <?php echo $cacheStats['cache_enabled'] ? '<span class="badge badge-success">已启用</span>' : '<span class="badge badge-danger">已禁用</span>'; ?> | 
                                已缓存查询: <?php echo $cacheStats['total_cached_queries']; ?> 个 | 
                                最大缓存数: <?php echo $cacheStats['max_cache_size_per_type']; ?> 个/类型
                                <?php if (!empty($cacheStats['cache_details'])): ?>
                                    <br>缓存详情: 
                                    <?php foreach ($cacheStats['cache_details'] as $type => $count): ?>
                                        <span class="badge badge-secondary"><?php echo $type; ?>: <?php echo $count; ?></span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                    
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="5" class="text-center">暂无用户数据</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user->id); ?></td>
                                        <td><?php echo htmlspecialchars($user->username); ?></td>
                                        <td><?php echo htmlspecialchars($user->email); ?></td>
                                        <td><?php echo htmlspecialchars($user->created_at); ?></td>
                                        <td>
                                            <a href="/rb_users/detail/<?php echo $user->id; ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <a href="/rb_users/edit/<?php echo $user->id; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm delete-user" 
                                                    data-id="<?php echo $user->id; ?>" 
                                                    data-username="<?php echo htmlspecialchars($user->username); ?>">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <strong id="deleteUserName"></strong> 吗？此操作不可恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确定删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 消息通知模态框 -->
<div class="modal fade" id="messageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">消息通知</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="messageContent"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>