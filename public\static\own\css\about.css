.about-page {
    padding: 20px 0;
}

.about-page h1 {
    color: #27ae60;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

.about-page h2 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
}

.about-page p {
    font-size: 1.1em;
    line-height: 1.6;
    color: #333;
    margin-bottom: 15px;
}

.intro {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    text-align: center;
}

.section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.section ul {
    margin: 15px 0;
    padding-left: 20px;
}

.section li {
    margin: 10px 0;
    font-size: 1.1em;
}

@media (max-width: 768px) {
    .about-page h1 {
        font-size: 2em;
    }
    
    .about-page {
        padding: 10px;
    }
    
    .intro, .section {
        padding: 15px;
    }
}