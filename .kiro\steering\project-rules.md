---
inclusion: always
---
当用户说"根据提示词"或"根据XX提示词"时，请严格按照以下流程操作：

**第一步：读取总纲**
- 必须首先读取`prompts/master_guidelines.md`作为项目的总纲
- 深入理解其中的框架规范、目录结构和核心原则
- 将总纲的原则作为所有操作的基础指导

**第二步：识别并查找提示词文件**
- 识别用户提到的提示词类型（如"根据页面管理提示词"），提取核心关键词（如"页面"）
- 在`prompts/`目录下搜索包含相关关键词的提示词文件
- 例如："根据布局提示词"应匹配包含"layout"关键词的提示词文件
- 例如："根据核心类提示词"应匹配包含"class"或"core"关键词的提示词文件
- 例如："根据测试提示词"应匹配包含"test"关键词的提示词文件
- 例如："根据页面提示词"应匹配包含"page"关键词的提示词文件（如`page_management_guidelines.md`）

**第三步：选择并读取提示词文件**
- 如果找到多个匹配文件或用户没有明确指定，则自动搜索并列出`prompts/`目录下的所有提示词文件，供用户选择
- 如果匹配到多个相关文件，则自动逐个读取所有相关文件的内容
- 读取选定的提示词文件内容，深入理解其中的所有规范、要求和流程
- 特别注意提示词中的具体指令、命名规范、文件结构要求等细节

**第四步：制定执行计划**
- 根据用户的具体需求、总纲规范和具体提示词中的指导原则，制定详细的执行计划
- 明确需要创建或修改的文件、需要配置的路由、需要添加的导航链接等所有操作步骤
- 例如：用户要求"根据页面提示词，建立首页文件"时，应：
  - 提取关键词"页面"找到对应提示词文件
  - 匹配提示词文件中关于"首页文件"的创建规范
  - 结合用户提供的具体内容要求制定计划

**第五步：执行并验证**
- 按照执行计划生成相应的代码和配置
- 严格遵循提示词中规定的命名规范、结构规范和最佳实践
- 在完成任务前，对照提示词要求进行全面检查，确保没有遗漏任何步骤或要求
- 提交整理后的完整结果，包括：总纲原则、提示词具体要求、以及最终生成的页面内容等

在生成代码和执行任务时，请特别注意以下要点：
- 严格遵循提示词中规定的文件命名规范和目录结构
- 确保代码注释完整且符合要求（使用中文注释）
- 遵循指定的编码规范（如PSR-12等）
- 保持与项目现有代码风格的一致性
- 根据需要创建相应的配套文件（如CSS、JS等）
- 始终遵循`master_guidelines.md`中定义的核心原则和框架约束
- 完成所有必须的配置步骤，包括但不限于路由配置、导航菜单更新等
- 在标记任务完成前，进行全面的自检，确保完全符合所有要求

如果用户没有明确指定使用哪个提示词文件，则根据任务类型智能推荐：
- 对于核心类创建任务，推荐使用`core_class_creation_guidelines.md`
- 对于布局文件创建任务，推荐使用`layout_file_guidelines.md`
- 对于测试相关任务，推荐使用`test_guidelines.md`
- 对于其他任务，推荐参考`master_guidelines.md`

系统会随着项目发展不断添加新的提示词文件，因此需要动态搜索和识别`prompts/`目录下的所有文件。