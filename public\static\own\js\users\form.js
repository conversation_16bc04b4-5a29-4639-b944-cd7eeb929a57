/**
 * 用户表单页面的JavaScript交互
 * 处理表单验证、取消按钮点击事件等
 */

/**
 * 从URL中获取参数
 * @param {string} name - 参数名
 * @returns {string|null} 参数值
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 获取来源页面的分页信息
 * @returns {string} 包含分页信息的路径
 */
function getReferrerPage() {
    // 尝试从URL参数中获取来源页面信息
    const sourcePage = getUrlParameter('sourcePage');
    if (sourcePage) {
        return sourcePage;
    }
    
    // 如果没有URL参数，尝试从document.referrer中获取
    if (document.referrer) {
        try {
            const referrerUrl = new URL(document.referrer);
            if (referrerUrl.pathname.includes('/users')) {
                // 如果来源是用户列表页，保留查询参数
                return referrerUrl.pathname + referrerUrl.search;
            }
        } catch (e) {
            console.error('解析来源URL失败:', e);
        }
    }
    
    // 默认返回用户列表页
    return '/users';
}

/**
 * 处理返回链接的显示逻辑和动态路径
 * 在iframe中隐藏，直接访问时显示，并设置正确的返回路径
 */
function handleBackToListLink() {
    const backToListLink = document.getElementById('backToListLink');
    if (backToListLink) {
        // 设置返回链接的动态路径
        backToListLink.href = getReferrerPage();
        
        // 检查是否在iframe中
        if (window.self !== window.top) {
            // 在iframe中，隐藏返回链接
            backToListLink.style.display = 'none';
        }
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 获取表单元素
    const userForm = document.getElementById('user-form');
    const cancelBtn = document.getElementById('cancel-btn');
    
    // 处理返回链接的显示逻辑
    handleBackToListLink();
    
    // 检查是否有添加成功的标记
    checkAddSuccessFlag();
    
    // 检查是否有编辑成功的标记
    checkEditSuccessFlag();
    
    // 为表单添加提交事件监听
    if (userForm) {
        userForm.addEventListener('submit', function(event) {
            // 执行客户端验证
            if (!validateForm()) {
                event.preventDefault(); // 阻止表单提交
            }
        });
    }
    
    // 为取消按钮添加点击事件监听
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            // 确认是否放弃表单数据
            if (confirm('确定要放弃当前表单数据吗？')) {
                window.location.href = getReferrerPage(); // 重定向到来源页面
            }
        });
    }
    
    // 添加输入框的输入事件监听，清除错误状态
    const inputElements = document.querySelectorAll('.form-group input');
    inputElements.forEach(input => {
        input.addEventListener('input', function() {
            // 移除错误样式
            this.classList.remove('error');
            
            // 移除对应的错误消息
            const errorElement = this.parentElement.querySelector('.error-text');
            if (errorElement) {
                errorElement.textContent = '';
            }
        });
    });
});

/**
 * 检查是否有添加成功的标记
 * 如果有，向父窗口发送消息关闭模态框并刷新列表
 */
function checkAddSuccessFlag() {
    const addSuccessFlag = document.getElementById('add-success');
    if (addSuccessFlag) {
        // 延迟发送消息，确保成功消息已经显示给用户
        setTimeout(() => {
            // 检查是否在iframe中
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息关闭模态框并刷新列表
                window.parent.postMessage({action: 'closeModal', success: true}, '*');
            } else {
                // 如果不在iframe中，则显示确认框
                if (confirm('用户添加成功，是否返回用户列表页？')) {
                    window.location.href = getReferrerPage();
                }
            }
        }, 500);
    }
}

/**
 * 检查是否有编辑成功的标记
 * 如果有，向父窗口发送消息关闭模态框并刷新列表
 */
function checkEditSuccessFlag() {
    // 检查是否有编辑成功的隐藏字段
    const editSuccessFlag = document.getElementById('edit-success');
    
    // 检查URL中是否包含编辑成功的标记或页面中有成功消息元素
    const urlParams = new URLSearchParams(window.location.search);
    const successMessage = document.querySelector('.success-message');
    
    // 如果有隐藏字段或URL中有成功参数或页面中有成功消息元素
    if (editSuccessFlag || urlParams.get('success') === 'true' || (successMessage && successMessage.textContent.includes('更新成功'))) {
        // 延迟发送消息，确保成功消息已经显示给用户
        setTimeout(() => {
            // 检查是否在iframe中
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息关闭模态框并刷新列表
                window.parent.postMessage({action: 'closeModal', success: true}, '*');
            } else if (editSuccessFlag) {
                // 如果不在iframe中且有编辑成功的隐藏字段，则显示确认框
                if (confirm('用户更新成功，是否返回用户列表页？')) {
                    window.location.href = getReferrerPage();
                }
            }
        }, 500);
    }
}

/**
 * 验证表单数据
 * @returns {boolean} 表单是否验证通过
 */
function validateForm() {
    let isValid = true;
    
    // 重置所有错误状态
    document.querySelectorAll('.form-group input.error').forEach(input => {
        input.classList.remove('error');
    });
    
    document.querySelectorAll('.error-text').forEach(element => {
        element.textContent = '';
    });
    
    // 验证用户名
    const username = document.getElementById('username').value.trim();
    if (username === '') {
        showError('username', '用户名不能为空');
        isValid = false;
    } else if (username.length < 2 || username.length > 20) {
        showError('username', '用户名长度必须在2-20个字符之间');
        isValid = false;
    } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
        showError('username', '用户名只能包含字母、数字、下划线和中文');
        isValid = false;
    }
    
    // 验证昵称
    const nickname = document.getElementById('nickname').value.trim();
    if (nickname === '') {
        showError('nickname', '昵称不能为空');
        isValid = false;
    } else if (nickname.length < 2 || nickname.length > 20) {
        showError('nickname', '昵称长度必须在2-20个字符之间');
        isValid = false;
    }
    
    // 验证邮箱
    const email = document.getElementById('email').value.trim();
    // 使用支持中文前缀的正则表达式
    // 用户名部分：支持中文、英文、数字、点、下划线和连字符
    const emailPattern = /^[\w\-\.\u4e00-\u9fa5]+@([\w-]+\.)+[\w-]+$/;
    if (email === '') {
        showError('email', '邮箱不能为空');
        isValid = false;
    } else if (!emailPattern.test(email)) {
        showError('email', '邮箱格式不正确');
        isValid = false;
    }
    
    // 验证年龄
    const age = document.getElementById('age').value.trim();
    if (age === '') {
        showError('age', '年龄不能为空');
        isValid = false;
    } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum) || ageNum < 1 || ageNum > 120) {
            showError('age', '年龄必须在1-120之间');
            isValid = false;
        }
    }
    
    // 验证性别选择
    const genderSelected = document.querySelector('input[name="gender"]:checked');
    if (!genderSelected) {
        const genderGroup = document.querySelector('input[name="gender"]').parentElement.parentElement.parentElement;
        const errorElement = genderGroup.querySelector('.error-text') || document.createElement('span');
        if (!errorElement.classList.contains('error-text')) {
            errorElement.className = 'error-text';
            genderGroup.appendChild(errorElement);
        }
        errorElement.textContent = '请选择性别';
        isValid = false;
    }
    
    // 验证状态选择
    const statusSelected = document.querySelector('input[name="status"]:checked');
    if (!statusSelected) {
        const statusGroup = document.querySelector('input[name="status"]').parentElement.parentElement.parentElement;
        const errorElement = statusGroup.querySelector('.error-text') || document.createElement('span');
        if (!errorElement.classList.contains('error-text')) {
            errorElement.className = 'error-text';
            statusGroup.appendChild(errorElement);
        }
        errorElement.textContent = '请选择状态';
        isValid = false;
    }
    
    return isValid;
}

/**
 * 显示表单错误信息
 * @param {string} fieldId - 字段ID
 * @param {string} message - 错误信息
 */
function showError(fieldId, message) {
    const field = document.getElementById(fieldId);
    if (field) {
        // 添加错误样式
        field.classList.add('error');
        
        // 查找或创建错误消息元素
        let errorElement = field.parentElement.querySelector('.error-text');
        if (!errorElement) {
            errorElement = document.createElement('span');
            errorElement.className = 'error-text';
            field.parentElement.appendChild(errorElement);
        }
        
        // 设置错误消息
        errorElement.textContent = message;
        
        // 聚焦到错误字段
        field.focus();
    }
}

/**
 * 重置表单错误状态
 */
function resetFormErrors() {
    // 移除所有输入框的错误样式
    document.querySelectorAll('.form-group input.error').forEach(input => {
        input.classList.remove('error');
    });
    
    // 清空所有错误消息
    document.querySelectorAll('.error-text').forEach(element => {
        element.textContent = '';
    });
}