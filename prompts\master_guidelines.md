# AiPHP提示词总纲

> AI助手特别提醒：当你看到用户提到"提示词"时，请自动查找并阅读本文件以及其他位于prompts目录下的文件，以确保理解任务要求和框架规范。

> 检索顺序（必须遵循）：
> 1) 读取 `docs/manifest.json`（若存在）；2) 读取 `docs/callable_contracts.md`；3) 读取本文件与 `prompts/` 其他文件；4) 读取 `docs/framework_flow.md`；5) 按清单继续。

> 硬性声明：若未按上述顺序先读取相应文档与页面提示词（当任务涉及页面时须先读 `prompts/page_management_guidelines.md`），则本次回答视为无效，应中止并先完成阅读。

## 概述

本文档是所有AiPHP框架提示词的总纲，定义了提示词的通用结构、规范和原则。所有具体的提示词都应该遵循本文档的约定。

## 架构说明

AiPHP是一个全新的架构，其主要特点是全面拥抱AI，不要以传统的MVC、依赖注入、控制反转、中间件等概念来理解本项目。

### 项目流程
项目的核心执行流程如下：
1. **入口文件**：通过`public/index.php`接收请求
2. **引导文件**：加载`config/bootstrap.php`，自动加载核心类
3. **路由处理**：根据路由配置文件`config/routes.php`中的配置，确定要加载的页面和路由参数
4. **调度中心**：通过调度中心加载对应的页面和布局
5. **页面渲染**：执行页面中的业务逻辑，结合布局进行渲染
6. **响应返回**：将最终结果返回给用户

### 业务处理原则
- 一切的业务逻辑都应在页面文件中处理
- 页面文件中可以直接使用核心类，无需手动加载
- 核心类提供通用功能支持，业务逻辑在页面层实现

## 提示词通用结构

### 1. 角色定义
```
你是PHP语言专家，熟悉PHP语言特性、最佳实践和编码规范，同时也是AiPHP框架的专家，熟悉框架的目录结构、设计原则和编码规范。
```

### 2. 任务目标
```
根据用户需求，在AiPHP框架中生成符合规范的代码。
```

### 3. 框架约束
```
必须严格遵循AiPHP框架的目录结构和命名规范：
- 配置文件放在config/目录下
- 业务逻辑放在handler/目录下
- 页面处理放在handler/pages/目录下
- 布局文件放在handler/layouts/目录下
- 用户编写的类库放在core/own-library/目录下
- 第三方类库放在core/other-library/目录下
- 用户静态资源放在public/static/own/目录下
- 第三方静态资源放在public/static/third-party/目录下
- 每个功能模块应建立独立的文件夹，因为有些功能不能仅由一个类完成
- 构建功能类时，只需关注功能类本身，不应包含使用该功能类的代码
- 类文件必须使用适当的命名空间
- 命名空间应与文件夹结构相对应
```

### 4. 输出规范
```
1. 仅输出必要的代码和说明
2. 不要包含额外的解释或评论
3. 严格按照AiPHP目录结构组织文件
4. 遵循PHP编码标准和最佳实践
5. 提供完整可运行的代码示例
```

## 核心原则

### 1. 约定大于配置
- 优先使用框架默认约定
- 避免不必要的配置文件
- 保持代码简洁明了

### 2. 极简主义
- 只生成必要的代码
- 避免过度设计
- 保持功能专注

### 3. 可扩展性
- 生成的代码应易于扩展
- 遵循开闭原则
- 保持松耦合

### 4. 页面管理规范
- 创建或修改页面时，必须同时阅读 `page_management_guidelines.md`，确保遵循完整的操作流程（如路由配置、导航更新等）。

### 4. AI友好性
- 代码结构清晰
- 命名规范一致
- 注释简洁明了

## 命名规范

### 1. 文件命名
- 使用小写字母
- 单词间用下划线分隔 (snake_case)
- 示例: user_controller.php, database_config.php

### 2. 文件夹命名
- 使用小驼峰命名法 (camelCase)
- 文件夹名应清晰表达其用途
- 示例: userManagement, databaseConfig, apiControllers

### 3. 类命名
- 使用大驼峰命名法 (PascalCase)
- 类名应清晰表达其用途
- 示例: UserController, DatabaseConfig

### 4. 方法命名
- 使用小驼峰命名法 (camelCase)
- 方法名应表达其行为
- 示例: getUserById, saveUserData

### 5. 变量命名
- 使用小驼峰命名法 (camelCase)
- 变量名应表达其内容
- 示例: userName, userData

## 代码生成规范

### 1. PHP代码
- 使用PHP 8.0及以上版本特性
- 严格类型声明
- 合理使用命名空间
- 遵循PSR-12编码规范

### 2. 注释规范
- 类必须有中英文档注释
- 公共方法必须有文档注释
- 复杂逻辑需要行内注释
- 使用简洁明了的中文注释

### 3. 错误处理
- 合理使用异常处理
- 提供清晰的错误信息
- 遵循框架的错误处理机制

## 终端执行规范

### 1. 命令执行要求
- 在终端执行命令时，每个命令必须独立执行，不能使用 && 连接符
- 终端命令应明确指定工作目录
- 对于需要按顺序执行的命令，应分别执行并检查每一步的结果
- 避免执行可能对系统造成损害的命令

### 2. 测试命令执行
- 执行测试命令时，应分别执行每个命令，确保前置条件满足
- 每个测试命令执行后应检查执行结果
- 测试过程中应避免对生产环境造成影响
- 不要使用 && 连接符来组合多个测试命令

### 3. 命令格式规范
- 终端命令应使用完整的路径或相对路径
- 避免使用可能引起歧义的缩写或别名
- 命令执行时应包含必要的参数和选项

## 特殊说明

### 1. 路径引用
- 使用相对路径引用框架内文件
- 遵循框架的自动加载机制

### 2. 配置文件
- 配置文件使用PHP数组格式
- 配置项应有清晰的注释说明

### 3. 安全性
- 输入验证是必须的
- 防止SQL注入和XSS攻击
- 遵循安全编码最佳实践

### 4. 核心文件保护
- 除非用户明确说明自己是开发者，否则不允许修改core文件夹内的任何内容
- 除非用户明确说明自己是开发者，否则不允许修改public/index.php入口文件
- 除非用户明确说明自己是开发者，否则不允许修改config/bootstrap.php引导文件
- 在任何情况下（建立、修改、调试、删除、测试），如果用户没有明确表示自己是开发者，都必须严格遵守上述限制

## 使用流程

1. 理解用户需求
2. 确定需要创建的文件类型和位置
3. 根据总纲和具体提示词模板生成代码
4. 确保代码符合AiPHP框架规范
5. 输出完整可运行的代码示例

---
*本文档是所有AiPHP提示词的基础，任何具体的提示词都必须遵循本文档的约定。*