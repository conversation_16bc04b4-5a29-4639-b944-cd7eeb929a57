<?php
/**
 * 缓存控制API端点
 * 处理缓存相关的操作
 */

header('Content-Type: application/json');

use Core\OtherLibrary\RedBean\RedBeanFacade as R;

try {
    // 获取操作类型
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'flush':
            // 清空缓存
            R::flushCache();
            $response = [
                'success' => true,
                'message' => '缓存已清空',
                'stats' => R::getCacheStats()
            ];
            break;
            
        case 'enable':
            // 启用缓存
            R::enableCache(true);
            $response = [
                'success' => true,
                'message' => '缓存已启用',
                'stats' => R::getCacheStats()
            ];
            break;
            
        case 'disable':
            // 禁用缓存
            R::enableCache(false);
            $response = [
                'success' => true,
                'message' => '缓存已禁用',
                'stats' => R::getCacheStats()
            ];
            break;
            
        case 'stats':
            // 获取缓存统计
            $response = [
                'success' => true,
                'message' => '获取缓存统计成功',
                'stats' => R::getCacheStats()
            ];
            break;
            
        default:
            $response = [
                'success' => false,
                'message' => '无效的操作类型'
            ];
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);