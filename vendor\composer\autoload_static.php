<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e
{
    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInit4b3f46cc5aaf35ab7ec7e7f8a4a9721e::$classMap;

        }, null, ClassLoader::class);
    }
}
