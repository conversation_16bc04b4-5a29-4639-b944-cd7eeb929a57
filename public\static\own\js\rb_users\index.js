/**
 * RedBeanPHP用户管理页面脚本
 * 对应页面: handler/pages/rb_users/index.php
 */

document.addEventListener('DOMContentLoaded', function() {
    // 当前要删除的用户ID
    let currentDeleteUserId = null;
    
    // 为所有删除按钮添加点击事件监听器
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            // 获取用户ID和用户名
            currentDeleteUserId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            
            // 设置模态框中的用户名
            document.getElementById('deleteUserName').textContent = username;
            
            // 显示删除确认模态框
            $('#deleteModal').modal('show');
        });
    });
    
    // 为确认删除按钮添加点击事件监听器
    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (currentDeleteUserId) {
            // 发送删除请求
            fetch('/rb_users/delete/' + currentDeleteUserId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    _token: '<?php echo $_SESSION['_token'] ?? ""; ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏删除确认模态框
                $('#deleteModal').modal('hide');
                
                if (data.success) {
                    // 显示成功消息
                    showMessage('用户删除成功！', 'success');
                    // 1.5秒后重新加载页面
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    // 显示错误消息
                    showMessage('删除失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                // 隐藏删除确认模态框
                $('#deleteModal').modal('hide');
                // 显示错误消息
                showMessage('删除请求失败: ' + error.message, 'error');
            });
        }
    });
    
    // 显示消息函数
    function showMessage(message, type) {
        const messageContent = document.getElementById('messageContent');
        messageContent.textContent = message;
        messageContent.className = type === 'success' ? 'text-success' : 'text-danger';
        $('#messageModal').modal('show');
        
        // 3秒后自动关闭消息模态框
        setTimeout(() => {
            $('#messageModal').modal('hide');
        }, 3000);
    }
});

// 简单的模态框实现（如果jQuery不可用）
if (typeof $ === 'undefined') {
    window.$ = function(selector) {
        if (selector.startsWith('#')) {
            const element = document.getElementById(selector.substring(1));
            return {
                modal: function(action) {
                    if (action === 'show') {
                        element.style.display = 'block';
                    } else if (action === 'hide') {
                        element.style.display = 'none';
                    }
                }
            };
        }
        return null;
    };
}