/**
 * 用户列表页面的JavaScript交互
 * 处理查看按钮点击事件、分页交互、编辑和添加用户模态框等
 */

// 当前要删除的用户ID
let currentDeleteUserId = null;

/**
 * 从URL中获取当前页码
 * @returns {number} 当前页码，如果URL中没有page参数则返回1
 */
function getCurrentPage() {
    const urlParams = new URLSearchParams(window.location.search);
    const pageParam = urlParams.get('page');
    return pageParam ? parseInt(pageParam) : 1;
}

/**
 * 构建包含当前页码的来源页面URL
 * @returns {string} 包含页码信息的来源URL
 */
function buildSourcePageUrl() {
    const currentPage = getCurrentPage();
    let sourceUrl = '/users';
    
    // 如果不是第一页，添加分页参数
    if (currentPage > 1) {
        sourceUrl += '?page=' + currentPage;
    }
    
    return sourceUrl;
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 为查看按钮添加点击事件
    addViewButtonListeners();
    
    // 为编辑按钮添加点击事件监听
    addEditButtonListeners();
    
    // 为添加用户按钮添加点击事件监听
    addAddUserButtonListener();
    
    // 初始化分页交互
    initPagination();
    
    // 添加表格行悬停效果
    enhanceTableInteraction();
    
    // 添加用户状态切换功能
    addStatusToggleListeners();
    
    // 添加用户删除功能
    addDeleteButtonListeners();
    
    // 初始化删除确认模态框事件
    initDeleteModal();
    
    // 创建模态框元素
    createModal();
});

/**
 * 为查看按钮添加点击事件监听器
 */
function addViewButtonListeners() {
    const viewButtons = document.querySelectorAll('.view-btn');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            // 跳转到用户详情页面，保留当前分页信息
            const currentPage = getCurrentPage();
            let redirectUrl = '/users/detail/' + userId;
            if (currentPage > 1) {
                redirectUrl += '?sourcePage=' + encodeURIComponent('/users?page=' + currentPage);
            }
            window.location.href = redirectUrl;
        });
    });
}

/**
 * 为编辑按钮添加点击事件监听
 */
function addEditButtonListeners() {
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            console.log('编辑按钮点击，用户ID:', userId);
            openModal('/users/form/' + userId);
        });
    });
}

/**
 * 为添加用户按钮添加点击事件监听
 */
function addAddUserButtonListener() {
    const addUserButton = document.getElementById('add-user-btn');
    if (addUserButton) {
        addUserButton.addEventListener('click', function() {
            openModal('/users/form');
        });
    }
}

/**
 * 创建模态框元素
 */
function createModal() {
    // 检查模态框是否已存在
    if (document.getElementById('user-modal')) {
        return;
    }
    
    // 创建模态框元素
    const modal = document.createElement('div');
    modal.id = 'user-modal';
    modal.className = 'modal';
    modal.style.display = 'none';
    
    // 创建模态框内容
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.width = '80%';
    modalContent.style.maxWidth = '700px';
    modalContent.style.height = 'auto';
    modalContent.style.overflow = 'visible';
    modalContent.style.maxHeight = '90vh';
    
    // 创建关闭按钮
    const closeButton = document.createElement('button');
    closeButton.className = 'modal-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', function() {
        closeModal();
    });
    
    // 创建iframe容器
    const iframeContainer = document.createElement('div');
    iframeContainer.className = 'modal-iframe-container';
    iframeContainer.style.height = 'auto';
    iframeContainer.style.overflow = 'visible';
    
    // 创建iframe
    const iframe = document.createElement('iframe');
    iframe.id = 'user-modal-iframe';
    iframe.className = 'modal-iframe';
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    iframe.style.minHeight = '500px';
    
    // 组装模态框
    iframeContainer.appendChild(iframe);
    modalContent.appendChild(closeButton);
    modalContent.appendChild(iframeContainer);
    modal.appendChild(modalContent);
    
    // 添加到文档中
    document.body.appendChild(modal);
    
    // 为模态框背景添加点击事件，点击背景关闭模态框
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

/**
 * 打开模态框
 * @param {string} url - 模态框中加载的URL
 */
function openModal(url) {
    const modal = document.getElementById('user-modal');
    const iframe = document.getElementById('user-modal-iframe');
    
    if (modal && iframe) {
        // 获取当前页码，并构建完整的URL（包含来源页面信息）
        const sourcePageUrl = buildSourcePageUrl();
        
        // 在URL中添加来源页面参数
        const separator = url.includes('?') ? '&' : '?';
        const fullUrl = url + separator + 'sourcePage=' + encodeURIComponent(sourcePageUrl);
        
        // 输出URL到控制台，用于调试
        console.log('打开模态框，URL:', fullUrl);
        
        // 设置iframe的src
        iframe.src = fullUrl;
        
        // 显示模态框
        modal.style.display = 'flex';
        
        // 添加动画效果
        setTimeout(() => {
            modal.classList.add('modal-open');
        }, 10);
    }
}

/**
 * 关闭模态框
 */
function closeModal() {
    const modal = document.getElementById('user-modal');
    const iframe = document.getElementById('user-modal-iframe');
    
    if (modal) {
        // 移除动画效果
        modal.classList.remove('modal-open');
        
        // 隐藏模态框
        setTimeout(() => {
            modal.style.display = 'none';
            
            // 重置iframe的src（可选，防止内存泄漏）
            if (iframe) {
                iframe.src = 'about:blank';
            }
            
            // 如果需要刷新页面（例如添加或编辑成功后），可以在这里处理
            // location.reload();
        }, 300);
    }
}

/**
 * 监听来自iframe的消息
 */
window.addEventListener('message', function(e) {
    // 处理关闭模态框的消息
    if (e.data && e.data.action === 'closeModal') {
        closeModal();
        
        // 如果操作成功，可以刷新页面
        if (e.data.success) {
            location.reload();
        }
    }
    // 处理调整iframe高度的消息
    else if (e.data && e.data.action === 'resizeIframe') {
        const iframe = document.getElementById('user-modal-iframe');
        const iframeContainer = document.querySelector('.modal-iframe-container');
        
        if (iframe && iframeContainer) {
            // 设置iframe高度以适应内容，添加一些额外的空间以避免出现滚动条
            const newHeight = Math.min(e.data.height + 20, window.innerHeight * 0.9);
            iframe.style.height = newHeight + 'px';
            iframeContainer.style.height = newHeight + 'px';
            
            // 确保模态框内容区域的高度也相应调整
            const modalContent = document.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.height = 'auto';
            }
        }
    }
});

/**
 * 初始化分页交互
 */
function initPagination() {
    const paginationLinks = document.querySelectorAll('.pagination-links a.page-link');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 这里可以添加加载状态指示器
            const loadingIndicator = document.createElement('span');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.textContent = '加载中...';
            loadingIndicator.style.marginLeft = '10px';
            
            this.parentNode.appendChild(loadingIndicator);
            
            // 在实际应用中，可以通过AJAX加载数据而不刷新整个页面
            // 这里暂时使用默认的链接跳转行为
        });
    });
}

/**
 * 增强表格交互体验
 */
function enhanceTableInteraction() {
    const tableRows = document.querySelectorAll('.users-table tbody tr');
    
    tableRows.forEach(row => {
        // 添加鼠标悬停效果
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
            this.style.transition = 'transform 0.2s ease';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        // 允许通过点击行选择用户（不显示弹出框）
        row.addEventListener('click', function(e) {
            // 避免与按钮点击冲突
            if (!e.target.closest('button')) {
                const userId = this.querySelector('.view-btn')?.getAttribute('data-id');
                if (userId) {
                    // 不显示弹出框，仅在控制台记录选择的用户ID
                    console.log('选择了用户ID: ' + userId);
                }
            }
        });
    });
}

/**
 * 搜索功能（预留）
 * 可以在未来添加搜索框和搜索逻辑
 */
function initSearchFunctionality() {
    // 预留的搜索功能实现
    // const searchInput = document.querySelector('#user-search');
    // searchInput.addEventListener('input', function() {
    //     const searchTerm = this.value.toLowerCase();
    //     filterUsers(searchTerm);
    // });
}

/**
 * 过滤用户列表（预留）
 */
function filterUsers(searchTerm) {
    // 预留的过滤功能实现
    // const tableRows = document.querySelectorAll('.users-table tbody tr');
    // tableRows.forEach(row => {
    //     const cells = row.querySelectorAll('td');
    //     let found = false;
    //     
    //     cells.forEach(cell => {
    //         if (cell.textContent.toLowerCase().includes(searchTerm)) {
    //             found = true;
    //         }
    //     });
    //     
    //     row.style.display = found ? '' : 'none';
    // });
}

/**
 * 添加用户状态切换的事件监听器
 */
function addStatusToggleListeners() {
    const statusBadges = document.querySelectorAll('.status-badge.clickable');
    
    statusBadges.forEach(badge => {
        badge.addEventListener('click', function() {
            // 获取用户ID和当前状态
            const userId = this.getAttribute('data-id');
            const currentStatus = parseInt(this.getAttribute('data-status'));
            const newStatus = currentStatus === 1 ? 0 : 1;
            
            // 保存原始内容，用于显示加载状态
            const originalText = this.textContent;
            
            // 显示加载状态
            this.classList.add('loading');
            this.textContent = '处理中...';
            
            // 获取CSRF令牌
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            // 发送实际的AJAX请求，包含CSRF令牌
            fetch('/users/update-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken
                },
                body: JSON.stringify({
                    id: userId,
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                // 移除加载状态
                this.classList.remove('loading');
                
                // 如果请求成功，更新徽章状态
                if (data.success) {
                    // 更新状态徽章的样式和文本
                    if (newStatus === 1) {
                        this.classList.remove('inactive');
                        this.classList.add('active');
                        this.textContent = '活跃';
                    } else {
                        this.classList.remove('active');
                        this.classList.add('inactive');
                        this.textContent = '禁用';
                    }
                    
                    // 更新数据属性
                    this.setAttribute('data-status', newStatus);
                    
                    // 检查响应中是否包含新的CSRF令牌，如果有则更新页面上的令牌
                    if (data.csrf_token) {
                        const csrfMetaTag = document.querySelector('meta[name="csrf-token"]');
                        if (csrfMetaTag) {
                            csrfMetaTag.setAttribute('content', data.csrf_token);
                        }
                    }
                } else {
                    // 显示错误消息
                    console.error('状态更新失败:', data.message);
                    alert('状态更新失败: ' + data.message);
                    this.textContent = originalText;
                }
            })
            .catch(error => {
                // 移除加载状态
                this.classList.remove('loading');
                
                // 显示错误消息
                console.error('请求失败:', error);
                alert('网络请求失败，请稍后重试');
                this.textContent = originalText;
            });
        });
    });
}

/**
 * 显示状态更新成功提示
 * @param {string} userId - 用户ID
 * @param {number} newStatus - 新状态（1表示活跃，0表示禁用）
 */
function showStatusUpdateSuccess(userId, newStatus) {
    // 创建临时提示元素
    const notification = document.createElement('div');
    notification.className = 'status-notification';
    notification.textContent = `用户ID: ${userId} 已${newStatus === 1 ? '启用' : '禁用'}`;
    
    // 添加样式
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.background = '#4caf50';
    notification.style.color = 'white';
    notification.style.padding = '10px 15px';
    notification.style.borderRadius = '4px';
    notification.style.zIndex = '1000';
    notification.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
    notification.style.transition = 'all 0.3s ease';
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 为删除按钮添加点击事件监听器
 */
function addDeleteButtonListeners() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            // 确保获取到的是字符串形式的ID
            if (!userId || userId.trim() === '') {
                showMessageNotification('删除失败: 未找到有效的用户ID', 'error');
                return;
            }
            
            // 获取用户名，使用更健壮的选择器
            let userName = '未知用户';
            try {
                const nameCell = this.closest('tr').querySelector('td:nth-child(2)');
                if (nameCell) {
                    userName = nameCell.textContent.trim();
                }
            } catch (e) {
                console.warn('无法获取用户名:', e);
            }
            
            // 设置当前要删除的用户信息
            currentDeleteUserId = userId.trim(); // 确保去除空白字符
            document.getElementById('delete-username').textContent = userName;
            
            // 显示删除确认模态框
            openDeleteConfirmModal();
        });
    });
}

/**
 * 初始化删除确认模态框事件
 */
function initDeleteModal() {
    const modal = document.getElementById('delete-confirm-modal');
    console.log('删除确认模态框元素:', modal);
    
    if (!modal) {
        console.error('未找到删除确认模态框元素');
        return;
    }
    
    const closeBtn = modal.querySelector('.modal-close');
    const cancelBtn = document.getElementById('cancel-delete');
    const confirmBtn = document.getElementById('confirm-delete');
    
    console.log('模态框按钮元素:', {closeBtn, cancelBtn, confirmBtn});
    
    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', closeDeleteConfirmModal);
    }
    
    // 取消按钮事件
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeDeleteConfirmModal);
    }
    
    // 确定按钮事件
    if (confirmBtn) {
        confirmBtn.addEventListener('click', performDeleteUser);
    }
    
    // 点击模态框背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeDeleteConfirmModal();
        }
    });
}

/**
 * 打开删除确认模态框
 */
function openDeleteConfirmModal() {
    const modal = document.getElementById('delete-confirm-modal');
    modal.classList.add('modal-open');
}

/**
 * 关闭删除确认模态框
 */
function closeDeleteConfirmModal() {
    const modal = document.getElementById('delete-confirm-modal');
    modal.classList.remove('modal-open');
    currentDeleteUserId = null;
}

/**
 * 执行用户删除操作
 */
function performDeleteUser() {
    if (!currentDeleteUserId) {
        showMessageNotification('删除失败: 用户ID为空', 'error');
        return;
    }
    
    // 确保用户ID是有效的
    const userId = currentDeleteUserId.toString().trim();
    if (userId === '') {
        showMessageNotification('删除失败: 用户ID无效', 'error');
        return;
    }
    
    // 关闭确认模态框
    closeDeleteConfirmModal();
    
    // 获取CSRF令牌
    const csrfMetaTag = document.querySelector('meta[name="csrf-token"]');
    if (!csrfMetaTag) {
        showMessageNotification('删除失败: CSRF令牌缺失', 'error');
        return;
    }
    
    const csrfToken = csrfMetaTag.getAttribute('content');
    
    // 准备发送的数据
    const requestData = {
        id: userId
    };
    
    // 发送删除请求
    fetch('/users/delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应错误: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // 删除成功，从表格中移除该行
            const row = document.querySelector(`.delete-btn[data-id="${userId}"]`).closest('tr');
            if (row) {
                row.remove();
            }
            // 显示成功消息
            showMessageNotification('用户删除成功', 'success');
        } else {
            // 显示错误消息
            showMessageNotification('删除失败: ' + data.message, 'error');
        }
        
        // 检查响应中是否包含新的CSRF令牌，如果有则更新页面上的令牌
        if (data.csrf_token) {
            const csrfMetaTag = document.querySelector('meta[name="csrf-token"]');
            if (csrfMetaTag) {
                csrfMetaTag.setAttribute('content', data.csrf_token);
            }
        }
        
        currentDeleteUserId = null;
    })
    .catch(error => {
        // 显示错误消息
        showMessageNotification('网络请求失败，请稍后重试: ' + error.message, 'error');
        currentDeleteUserId = null;
    });
}

/**
 * 显示消息通知
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('success' 或 'error')
 */
function showMessageNotification(message, type) {
    const modal = document.getElementById('message-notification');
    const messageElement = document.getElementById('notification-message');
    
    // 设置消息内容
    messageElement.textContent = message;
    
    // 设置样式类型
    modal.querySelector('.modal-content').className = 'modal-content notification-modal';
    if (type === 'error') {
        modal.querySelector('.modal-content').classList.add('error');
    }
    
    // 显示模态框
    modal.classList.add('modal-open');
    
    // 3秒后自动关闭
    setTimeout(() => {
        modal.classList.remove('modal-open');
    }, 3000);
}