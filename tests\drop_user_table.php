<?php
/**
 * 删除user表脚本
 * 
 * 用于删除数据库中的空表user
 */

// 定义所有必需的路径常量
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('HANDLER_PATH', ROOT_PATH . '/handler');
define('PAGES_PATH', HANDLER_PATH . '/pages');
define('LAYOUTS_PATH', HANDLER_PATH . '/layouts');
define('OWN_LIBRARY_PATH', CORE_PATH . '/own-library');
define('OTHER_LIBRARY_PATH', CORE_PATH . '/other-library');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STATIC_PATH', PUBLIC_PATH . '/static');

// 注册自动加载函数
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

// 使用自动加载引入RedBeanPHP类和门面类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

echo "删除user表脚本...\n";

try {
    // 初始化RedBeanPHP
    echo "\n=== 初始化RedBeanPHP ===\n";
    R::initialize();
    echo "RedBeanPHP初始化成功\n";
    
    // 检查数据库连接
    echo "\n=== 检查数据库连接 ===\n";
    $test = \R::testConnection();
    if ($test) {
        echo "数据库连接成功\n";
    } else {
        echo "数据库连接失败\n";
        exit(1);
    }
    
    // 获取所有表
    echo "\n=== 数据库中的所有表 ===\n";
    $tables = \R::inspect();
    if (!empty($tables)) {
        foreach ($tables as $table) {
            echo "- " . $table . "\n";
        }
    } else {
        echo "数据库中没有表\n";
    }
    
    // 检查user表是否存在
    echo "\n=== 检查user表 ===\n";
    if (in_array('user', $tables)) {
        echo "user表存在，准备删除...\n";
        
        // 删除user表
        echo "\n=== 删除user表 ===\n";
        \R::exec('DROP TABLE user');
        echo "user表删除成功\n";
    } else {
        echo "user表不存在\n";
    }
    
    // 再次获取所有表
    echo "\n=== 删除后的数据库表 ===\n";
    $tables = \R::inspect();
    if (!empty($tables)) {
        foreach ($tables as $table) {
            echo "- " . $table . "\n";
        }
    } else {
        echo "数据库中没有表\n";
    }
    
    echo "\n操作完成。\n";
    
} catch (Exception $e) {
    echo "操作出错: " . $e->getMessage() . "\n";
    echo "错误跟踪: " . $e->getTraceAsString() . "\n";
}