/* 用户详情页面样式 */
.user-detail-page {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.page-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 24px;
}

.back-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #16a085;
    color: white !important; /* 强制文字为白色 */
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.back-button:hover {
    background-color: #138d75;
}

.error-message {
    padding: 15px;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 20px;
}

.user-detail-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.user-info-section,
.user-timeline-section {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-info-section h2,
.user-timeline-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 18px;
    border-bottom: 2px solid #16a085;
    padding-bottom: 8px;
}

.user-info-table {
    width: 100%;
    border-collapse: collapse;
}

.user-info-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
}

.user-info-table tr:last-child td {
    border-bottom: none;
}

.user-info-table .label {
    width: 30%;
    font-weight: 600;
    color: #5a6c7d;
    vertical-align: top;
}

.user-info-table .value {
    color: #2c3e50;
}

.status-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* 删除了用户操作按钮的样式 */

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .user-info-table .label {
        width: 40%;
    }
    
    /* 删除了用户操作按钮的响应式样式 */
}