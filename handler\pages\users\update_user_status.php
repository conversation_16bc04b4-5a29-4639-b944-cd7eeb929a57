<?php
/**
 * 用户状态更新接口
 * 
 * 处理前端发送的用户状态更新请求
 * 接收用户ID和新的状态值，更新到数据库
 */

// 设置返回JSON格式
header('Content-Type: application/json');

// 使用必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;
use Core\OwnLibrary\Security\CsrfTokenManager;

// 实例化CSRF令牌管理器
$csrfManager = new CsrfTokenManager();

// 初始化响应数组
$response = [
    'success' => false,
    'message' => ''
];

try {
    // 检查请求方法是否为POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法错误，只支持POST请求');
    }
    
    // 验证CSRF令牌
    if (!$csrfManager->validateRequestToken()) {
        throw new Exception('CSRF验证失败，请刷新页面后重试');
    }
    
    // 获取请求数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证参数
    if (!isset($data['id']) || !isset($data['status'])) {
        throw new Exception('参数不完整，缺少用户ID或状态值');
    }
    
    // 验证用户ID和状态值
    $userId = (int)$data['id'];
    $status = (int)$data['status'];
    
    if ($userId <= 0 || ($status !== 0 && $status !== 1)) {
        throw new Exception('参数值无效');
    }
    
    // 加载用户
    $user = R::load('users', $userId);
    
    if (!$user->id) {
        throw new Exception('未找到指定用户');
    }
    
    // 更新用户状态
    $user->status = $status;
    R::store($user);
    
    $response['success'] = true;
    $response['message'] = '用户状态更新成功';
    // 在响应中包含新生成的CSRF令牌
    $response['csrf_token'] = $csrfManager->getToken();
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// 返回JSON响应
echo json_encode($response);
exit;