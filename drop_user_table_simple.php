<?php
// 简单的删除user表脚本

require_once 'core/other-library/redbeanphp/rb.php';

// 设置数据库连接
\R::setup('sqlite:database/database.sqlite');

// 检查user表是否存在并删除
$tables = \R::inspect();
if (in_array('user', $tables)) {
    echo "正在删除user表...\n";
    \R::exec('DROP TABLE `user`');
    echo "user表已删除。\n";
} else {
    echo "user表不存在。\n";
}

// 显示所有表
echo "当前数据库中的表：\n";
$tables = \R::inspect();
foreach ($tables as $table) {
    echo "- " . $table . "\n";
}