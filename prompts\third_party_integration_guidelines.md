# 第三方类库集成指南

## 概述
本指南描述了在AiPHP框架中集成第三方类库的标准流程和最佳实践。

## 集成流程

### 1. 准备工作
- 确定第三方类库的命名空间结构
- 确定类库文件的存放位置（通常在core/other-library/目录下）
- 检查类库是否符合项目技术栈要求

### 2. 文件存放规范
- 将第三方类库文件存放在`core/other-library/{类库名称}/`目录下
- 保持类库原有的文件结构和命名空间
- 如需创建门面类，应在同一目录下创建

### 3. 自动加载配置
- 在需要使用第三方类库的文件中，配置自动加载器
- 添加命名空间映射以支持第三方类库的自动加载
- 示例配置：
```php
$autoloader = new Autoloader();
$autoloader->addNamespace('第三方命名空间', CORE_PATH . '/other-library/类库目录');
$autoloader->register();
```

### 4. 门面类创建（如需要）
- 为简化使用，可创建门面类封装第三方类库的功能
- 门面类应放在同一目录下
- 门面类应使用项目命名空间：`Core\OtherLibrary\{类库名称}`

### 5. 测试验证
- 创建独立的测试文件验证第三方类库功能
- 测试文件必须存放在`tests`目录下
- 测试文件结构要求：
  - 文件上半部分包含测试引导代码（自动加载配置等）
  - 文件下半部分包含具体的测试代码
- 在测试文件中使用自动加载而不是手动引入
- 验证所有核心功能正常工作

### 6. 文档记录
- 更新相关文档说明第三方类库的使用方法
- 记录集成过程中遇到的问题和解决方案

## 最佳实践

### 命名空间处理
- 保留第三方类库原有的命名空间结构
- 通过自动加载器配置支持命名空间解析
- 避免修改第三方类库的源代码

### 兼容性考虑
- 确保第三方类库与项目PHP版本兼容
- 检查是否有与其他依赖的冲突
- 考虑性能影响

### 错误处理
- 正确处理第三方类库可能抛出的异常
- 在门面类中封装错误处理逻辑
- 提供统一的错误信息格式

## 示例

### 集成Medoo ORM的示例
```php
// 测试文件上半部分：引导代码
<?php
// 引入测试引导文件
require_once __DIR__ . '/test_bootstrap.php';

// 使用自动加载引入类
use Medoo\Medoo;
use Core\OtherLibrary\Medoo\MedooFacade as DB;

// 测试文件下半部分：具体测试代码
echo "第三方Medoo类库功能测试...\n";
$users = DB::select('users', ['id', 'username']);
echo "查询到 " . count($users) . " 条记录\n";
```

## 注意事项
- 不要修改第三方类库的源代码
- 遵循第三方类库的许可协议
- 定期检查第三方类库的安全更新