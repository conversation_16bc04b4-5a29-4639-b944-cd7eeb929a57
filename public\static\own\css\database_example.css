/**
 * 数据库操作示例页面样式
 * 
 * 用于美化数据库操作示例页面的显示效果
 */

.database-example-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.database-example-page h1 {
    color: #2d6a4f;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

.database-example-page h2 {
    color: #40916c;
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 1.8em;
    border-bottom: 2px solid #40916c;
    padding-bottom: 10px;
}

.database-example-page h3 {
    color: #1b4332;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.example-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.example-section p {
    line-height: 1.6;
    color: #333;
    margin-bottom: 15px;
}

.example-section ul {
    margin: 15px 0;
    padding-left: 20px;
}

.example-section li {
    margin-bottom: 10px;
    line-height: 1.6;
}

pre {
    background-color: #2d2d2d;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 15px 0;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

code {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 14px;
}