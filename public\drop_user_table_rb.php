<?php
/**
 * 使用RedBeanPHP删除user表脚本（通过Web访问）
 * 
 * 用于删除数据库中的空表user
 */

// 定义所有必需的路径常量
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('HANDLER_PATH', ROOT_PATH . '/handler');
define('PAGES_PATH', HANDLER_PATH . '/pages');
define('LAYOUTS_PATH', HANDLER_PATH . '/layouts');
define('OWN_LIBRARY_PATH', CORE_PATH . '/own-library');
define('OTHER_LIBRARY_PATH', CORE_PATH . '/other-library');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STATIC_PATH', PUBLIC_PATH . '/static');

// 注册自动加载函数
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

// 使用自动加载引入RedBeanPHP类和门面类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

echo "<h1>使用RedBeanPHP删除user表脚本</h1>\n";

try {
    // 初始化RedBeanPHP
    echo "<h2>初始化RedBeanPHP</h2>\n";
    R::initialize();
    echo "<p>RedBeanPHP初始化成功</p>\n";
    
    // 检查数据库连接
    echo "<h2>检查数据库连接</h2>\n";
    $test = \R::testConnection();
    if ($test) {
        echo "<p>数据库连接成功</p>\n";
    } else {
        echo "<p style='color: red;'>数据库连接失败</p>\n";
        exit(1);
    }
    
    // 获取所有表
    echo "<h2>数据库中的所有表</h2>\n";
    $tables = \R::inspect();
    if (!empty($tables)) {
        echo "<ul>\n";
        foreach ($tables as $table) {
            echo "<li>" . htmlspecialchars($table) . "</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>数据库中没有表</p>\n";
    }
    
    // 检查user表是否存在
    echo "<h2>检查user表</h2>\n";
    if (in_array('user', $tables)) {
        echo "<p>user表存在，准备删除...</p>\n";
        
        // 使用RedBeanPHP的方式删除表
        echo "<h2>使用RedBeanPHP删除user表</h2>\n";
        \R::exec('DROP TABLE `user`');
        echo "<p style='color: green;'>user表删除成功</p>\n";
    } else {
        echo "<p>user表不存在</p>\n";
    }
    
    // 再次获取所有表
    echo "<h2>删除后的数据库表</h2>\n";
    $tables = \R::inspect();
    if (!empty($tables)) {
        echo "<ul>\n";
        foreach ($tables as $table) {
            echo "<li>" . htmlspecialchars($table) . "</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p>数据库中没有表</p>\n";
    }
    
    echo "<p style='color: green; font-weight: bold;'>操作完成。</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>操作出错: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}