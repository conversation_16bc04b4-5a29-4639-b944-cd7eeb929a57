<?php
/**
 * 用户表单页面
 * 
 * 用于添加和编辑用户信息，不使用布局文件
 */

// 引入必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;
use Core\OwnLibrary\Validation\ValidationHelper;
use Core\OwnLibrary\Security\CsrfTokenManager;

// 页面描述注释
// 不使用布局

// 页面元数据
$pageTitle = '用户表单 - AiPHP应用';
$pageDescription = '用于添加和编辑用户信息的表单页面';
$pageKeywords = '用户表单,添加用户,编辑用户,AiPHP';

// 实例化CSRF令牌管理器
$csrfManager = new CsrfTokenManager();

// 生成CSRF令牌
$csrfToken = $csrfManager->getToken();

// 初始化用户数据
$user = [
    'id' => '',
    'username' => '',
    'nickname' => '',
    'email' => '',
    'gender' => '1',
    'age' => '',
    'status' => '1'
];

// 错误消息数组
$errors = [];

// 成功消息
$successMessage = '';

// 检查是否是编辑模式
$isEditMode = false;
$userId = 0;

// 检查是否有路由参数id
if(isset($params) && isset($params['id'])){
    $isEditMode = true;
    $userId = (int)$params['id'];
    
    try {
        // 查询用户数据
        $userData = R::load('users', $userId);
        
        if ($userData->id) {
            $user = [
                'id' => $userData->id,
                'username' => $userData->username,
                'nickname' => $userData->nickname,
                'email' => $userData->email,
                'gender' => $userData->gender,
                'age' => $userData->age,
                'status' => $userData->status
            ];
        } else {
            $errors['general'] = '用户不存在';
        }
    } catch (Exception $e) {
        $errors['general'] = '数据库操作失败: ' . $e->getMessage();
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 验证CSRF令牌
    if (!$csrfManager->validateToken($_POST['csrf_token'] ?? '')) {
        $errors['general'] = 'CSRF令牌验证失败，表单提交无效';
    } else {
        // 检查是否是编辑模式（从POST数据中获取id）
        $postIsEditMode = isset($_POST['id']) && $_POST['id'] !== '';
        $postUserId = $postIsEditMode ? (int)$_POST['id'] : 0;
        
        // 收集表单数据
        $formData = [
            'username' => $_POST['username'] ?? '',
            'password' => $_POST['password'] ?? '',
            'nickname' => $_POST['nickname'] ?? '',
            'email' => $_POST['email'] ?? '',
            'gender' => $_POST['gender'] ?? '1',
            'age' => $_POST['age'] ?? '',
            'status' => $_POST['status'] ?? '1'
        ];
        
        // 验证代码已注释掉
        // 直接设置$errors为空数组以便继续执行
        $errors = [];
        
        // 恢复验证代码并修复正则表达式
        $errors = array_merge(
            ValidationHelper::validate($formData, 'password', [
                '必填' => '密码不能为空',
                '长度6-20' => '密码长度必须在6-20个字符之间'
            ]),
            ValidationHelper::validate($formData, 'username', [
                '必填' => '用户名不能为空',
                '长度2-20' => '用户名长度必须在2-20个字符之间',
                '正则(/^[a-zA-Z0-9_\\x{4e00}-\\x{9fa5}]+$/u)' => '用户名只能包含字母、数字、下划线和中文'
            ]),
            ValidationHelper::validate($formData, 'nickname', [
                '必填' => '昵称不能为空',
                '长度2-20' => '昵称长度必须在2-20个字符之间'
            ]),
            // 使用自定义正则表达式验证邮箱，支持中文前缀
            ValidationHelper::validate($formData, 'email', [
                '必填' => '邮箱不能为空',
                '正则(/^[\\w\\-\\.\\x{4e00}-\\x{9fa5}]+@([\\w-]+\\.)+[\\w-]+$/u)' => '邮箱格式不正确'
            ]),
            ValidationHelper::validate($formData, 'gender', [
                '必填' => '请选择性别',
                '枚举0,1' => '性别选择无效'
            ]),
            ValidationHelper::validate($formData, 'age', [
                '必填' => '年龄不能为空',
                '数字' => '年龄必须是数字',
                '范围1-120' => '年龄必须在1-120之间'
            ]),
            ValidationHelper::validate($formData, 'status', [
                '必填' => '请选择状态',
                '枚举0,1' => '状态选择无效'
            ])
        );
        
        // 如果没有验证错误，检查用户名和邮箱唯一性
        if (empty($errors)) {
            try {
                // 检查用户名是否已存在
                $usernameExists = false;
                $emailExists = false;
                
                if ($postIsEditMode && $postUserId > 0) {
                    // 编辑模式：检查除当前用户外是否已存在相同用户名或邮箱
                    $existingUser = R::findOne('users', 
                        'username = ? AND id != ?', 
                        [$formData['username'], $postUserId]);
                    $usernameExists = $existingUser !== null;
                    
                    $existingEmail = R::findOne('users', 
                        'email = ? AND id != ?', 
                        [$formData['email'], $postUserId]);
                    $emailExists = $existingEmail !== null;
                } else {
                    // 添加模式：检查是否已存在相同用户名或邮箱
                    $existingUser = R::findOne('users', 'username = ?', [$formData['username']]);
                    $usernameExists = $existingUser !== null;
                    
                    $existingEmail = R::findOne('users', 'email = ?', [$formData['email']]);
                    $emailExists = $existingEmail !== null;
                }
                
                // 如果存在重复，添加错误信息
                if ($usernameExists) {
                    $errors['username'] = '该用户名已被使用，请选择其他用户名';
                }
                
                if ($emailExists) {
                    $errors['email'] = '该邮箱地址已被使用，请使用其他邮箱';
                }
                
                // 检查是否有用户名或邮箱重复错误，如果有则不执行数据库操作
                if (empty($errors)) {
                    if ($postIsEditMode && $postUserId > 0) {
                        // 编辑模式：更新用户
                        $userBean = R::load('users', $postUserId);
                        if ($userBean->id) {
                            $userBean->username = $formData['username'];
                            $userBean->nickname = $formData['nickname'];
                            $userBean->email = $formData['email'];
                            $userBean->gender = (int)$formData['gender'];
                            $userBean->age = (int)$formData['age'];
                            $userBean->status = (int)$formData['status'];
                            $userBean->updated_at = date('Y-m-d H:i:s');
                            
                            R::store($userBean);
                            
                            $successMessage = '用户信息更新成功';
                            // 检测是否在iframe中
                            // 通过隐藏字段设置编辑成功标记，让form.js中的checkEditSuccessFlag函数处理iframe检测和消息发送
                            echo '<input type="hidden" id="edit-success" value="1">';
                        } else {
                            $errors['general'] = '用户不存在';
                        }
                    } else {
                        // 添加模式：插入新用户
                        $userBean = R::dispense('users');
                        $userBean->username = $formData['username'];
                        $userBean->password = password_hash($formData['password'], PASSWORD_DEFAULT); // 使用PHP内置函数哈希密码
                        $userBean->nickname = $formData['nickname'];
                        $userBean->email = $formData['email'];
                        $userBean->gender = (int)$formData['gender'];
                        $userBean->age = (int)$formData['age'];
                        $userBean->status = (int)$formData['status'];
                        $userBean->created_at = date('Y-m-d H:i:s');
                        $userBean->updated_at = date('Y-m-d H:i:s');
                        
                        R::store($userBean);
                        
                        $successMessage = '用户添加成功';
                        // 清空表单数据
                        $user = [
                            'id' => '',
                            'username' => '',
                            'nickname' => '',
                            'email' => '',
                            'gender' => '1',
                            'age' => '',
                            'status' => '1'
                        ];
                        // 通过隐藏字段设置添加成功标记，让form.js中的checkAddSuccessFlag函数处理iframe检测和消息发送
                        echo '<input type="hidden" id="add-success" value="1">';
                    }
                }
            } catch (Exception $e) {
                $errors['general'] = '数据库操作失败: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    <!-- 引用CSS文件，禁止在页面中直接写CSS样式 -->
    <link rel="stylesheet" href="/static/own/css/users/form.css">
</head>
<body>
    <div class="user-form-container">
        <div class="form-header">
            <h1><?php echo $isEditMode ? '编辑用户' : '添加用户'; ?></h1>
            <a href="/users" class="back-link" id="backToListLink">返回用户列表</a>
        </div>
        
        <?php if (isset($errors['general'])): ?>
            <div class="error-message"><?php echo htmlspecialchars($errors['general']); ?></div>
        <?php endif; ?>
        
        <?php if ($successMessage): ?>
            <div class="success-message"><?php echo htmlspecialchars($successMessage); ?></div>
        <?php endif; ?>
        
        <form id="user-form" method="POST" action="/users/save">
            <!-- CSRF令牌 -->
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
            
            <?php if ($isEditMode): ?>
                <input type="hidden" name="id" value="<?php echo htmlspecialchars($user['id']); ?>">
            <?php endif; ?>
            
            <div class="form-group">
                <label for="username">用户名 *</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($user['username']); ?>" 
                    placeholder="请输入用户名" 
                    class="<?php echo isset($errors['username']) ? 'error' : ''; ?>"
                >
                <?php if (isset($errors['username'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['username']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="password">密码 *</label>
                <div style="position: relative;">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        placeholder="请输入密码（6-20个字符）" 
                        class="<?php echo isset($errors['password']) ? 'error' : ''; ?>"
                        oninput="checkPasswordStrength(this.value)"
                        
                    >
                    <button 
                        type="button" 
                        id="toggle-password" 
                        onclick="togglePasswordVisibility()"
                        aria-label="切换密码可见性"
                        style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; padding: 5px;"
                    >
                    </button>
                </div>
                
                <!-- 密码强度指示器 -->
                <div style="margin-top: 5px; font-size: 12px; color: #666;">
                    <div style="display: flex; gap: 3px; margin-bottom: 2px;">
                        <div id="strength-bar-1" style="flex: 1; height: 3px; background-color: #e0e0e0;"></div>
                        <div id="strength-bar-2" style="flex: 1; height: 3px; background-color: #e0e0e0;"></div>
                        <div id="strength-bar-3" style="flex: 1; height: 3px; background-color: #e0e0e0;"></div>
                        <div id="strength-bar-4" style="flex: 1; height: 3px; background-color: #e0e0e0;"></div>
                    </div>
                    <p id="strength-text" style="margin: 2px 0 0 0;">密码强度: 请输入密码</p>
                </div>
                
                <?php if (isset($errors['password'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['password']); ?></span>
                <?php endif; ?>
                
                <script type="text/javascript">
                    // 切换密码可见性
                    function togglePasswordVisibility() {
                        const passwordInput = document.getElementById('password');
                        const toggleBtn = document.getElementById('toggle-password');
                        const icon = toggleBtn.querySelector('svg');
                        
                        if (passwordInput.type === 'password') {
                            passwordInput.type = 'text';
                            icon.innerHTML = '<path d="M1 8s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8zm13.59 2.92l-2.12-2.12c.54-.6 1.33-1.02 2.12-1.02.78 0 1.58.42 2.11 1.02l-2.11 2.12zm-4.24-4.24l-2.12-2.12c.27-.61 1.02-1.02 1.75-1.02.73 0 1.48.41 1.76 1.02l-1.76 2.12zM8 2.92L10.12 5 8 7.08 5.88 5 8 2.92zm-4.24 9.4l2.12-2.12c-.54-.6-1.33-1.02-2.12-1.02-.78 0-1.58.42-2.11 1.02l2.11 2.12zm4.24-4.24l2.12-2.12c-.27-.61-1.02-1.02-1.75-1.02-.73 0-1.48.41-1.76 1.02l1.76 2.12zm1.59 1.59l2.12-2.12c-.6-.54-1.49-.92-2.12-.92-.78 0-1.58.42-2.11 1.02l2.11 2.12c.53-.6 1.32-1.02 2.12-1.02.73 0 1.48.41 1.76 1.02zM8 13.08L5.88 11 8 8.92 10.12 11 8 13.08z"/>';
                        } else {
                            passwordInput.type = 'password';
                            icon.innerHTML = '<path d="M8 1a7 7 0 100 14A7 7 0 008 1zm0 12a5 5 0 110-10 5 5 0 010 10z"/><path d="M10.5 8a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>';
                        }
                    }
                    
                    // 检查密码强度
                    function checkPasswordStrength(password) {
                        // 重置所有强度条
                        for (let i = 1; i <= 4; i++) {
                            const bar = document.getElementById(`strength-bar-${i}`);
                            bar.style.backgroundColor = '#e0e0e0';
                        }
                        
                        const strengthText = document.getElementById('strength-text');
                        
                        if (!password) {
                            strengthText.textContent = '密码强度: 请输入密码';
                            return;
                        }
                        
                        // 简单的密码强度检查
                        let strength = 0;
                        
                        // 长度检查
                        if (password.length >= 6) strength++;
                        if (password.length >= 10) strength++;
                        
                        // 字符类型检查
                        if (/[A-Z]/.test(password)) strength++;
                        if (/[0-9]/.test(password) && /[^A-Za-z0-9]/.test(password)) strength++;
                        
                        // 限制强度在1-4之间
                        const finalStrength = Math.min(strength, 4);
                        
                        // 更新强度条
                        const colors = ['#f44336', '#ff9800', '#ffeb3b', '#4CAF50'];
                        for (let i = 1; i <= finalStrength; i++) {
                            const bar = document.getElementById(`strength-bar-${i}`);
                            bar.style.backgroundColor = colors[finalStrength - 1];
                        }
                        
                        // 更新文本
                        const strengthLabels = ['非常弱', '弱', '中等', '强'];
                        strengthText.textContent = `密码强度: ${strengthLabels[finalStrength - 1]}`;
                    }
                </script>
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称 *</label>
                <input 
                    type="text" 
                    id="nickname" 
                    name="nickname" 
                    value="<?php echo htmlspecialchars($user['nickname']); ?>" 
                    placeholder="请输入昵称" 
                    class="<?php echo isset($errors['nickname']) ? 'error' : ''; ?>"
                >
                <?php if (isset($errors['nickname'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['nickname']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱 *</label>
                <input 
                    type="text" 
                    id="email" 
                    name="email" 
                    value="<?php echo htmlspecialchars($user['email']); ?>" 
                    placeholder="请输入邮箱" 
                    class="<?php echo isset($errors['email']) ? 'error' : ''; ?>"
                >
                <?php if (isset($errors['email'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['email']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label>性别 *</label>
                <div class="radio-group">
                    <label class="radio-option">
                        <input 
                            type="radio" 
                            name="gender" 
                            value="1" 
                            <?php echo $user['gender'] == 1 ? 'checked' : ''; ?>
                        >
                        <span>男</span>
                    </label>
                    <label class="radio-option">
                        <input 
                            type="radio" 
                            name="gender" 
                            value="0" 
                            <?php echo $user['gender'] == 0 ? 'checked' : ''; ?>
                        >
                        <span>女</span>
                    </label>
                </div>
                <?php if (isset($errors['gender'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['gender']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="age">年龄 *</label>
                <input 
                    type="number" 
                    id="age" 
                    name="age" 
                    value="<?php echo htmlspecialchars($user['age']); ?>" 
                    placeholder="请输入年龄" 
                    min="1" 
                    max="120" 
                    class="<?php echo isset($errors['age']) ? 'error' : ''; ?>"
                >
                <?php if (isset($errors['age'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['age']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label>状态 *</label>
                <div class="radio-group">
                    <label class="radio-option">
                        <input 
                            type="radio" 
                            name="status" 
                            value="1" 
                            <?php echo $user['status'] == 1 ? 'checked' : ''; ?>
                        >
                        <span>活跃</span>
                    </label>
                    <label class="radio-option">
                        <input 
                            type="radio" 
                            name="status" 
                            value="0" 
                            <?php echo $user['status'] == 0 ? 'checked' : ''; ?>
                        >
                        <span>禁用</span>
                    </label>
                </div>
                <?php if (isset($errors['status'])): ?>
                    <span class="error-text"><?php echo htmlspecialchars($errors['status']); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="submit-btn"><?php echo $isEditMode ? '更新' : '添加'; ?></button>
                <button type="button" id="cancel-btn" class="cancel-btn">取消</button>
            </div>
        </form>
    </div>
    
    <!-- 引用JS文件，禁止在页面中直接写JavaScript代码 -->
    <script src="/static/own/js/users/form.js"></script>
</body>
</html>