/**
 * RedBeanPHP用户表单页面脚本
 * 对应页面: handler/pages/rb_users/form.php
 */

document.addEventListener('DOMContentLoaded', function() {
    // 表单验证
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // 获取表单字段
            const username = document.getElementById('username');
            const email = document.getElementById('email');
            
            // 验证用户名
            if (!username || !username.value.trim()) {
                e.preventDefault();
                alert('请输入用户名');
                username.focus();
                return;
            }
            
            // 验证邮箱
            if (!email || !email.value.trim()) {
                e.preventDefault();
                alert('请输入邮箱');
                email.focus();
                return;
            }
            
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email.value)) {
                e.preventDefault();
                alert('请输入有效的邮箱地址');
                email.focus();
                return;
            }
        });
    }
    
    // 为取消按钮添加确认对话框
    const cancelButton = document.querySelector('a[href="/rb_users"]');
    if (cancelButton) {
        cancelButton.addEventListener('click', function(e) {
            if (formHasUnsavedChanges()) {
                if (!confirm('您有未保存的更改，确定要离开此页面吗？')) {
                    e.preventDefault();
                }
            }
        });
    }
    
    // 检查表单是否有未保存的更改
    function formHasUnsavedChanges() {
        const username = document.getElementById('username');
        const email = document.getElementById('email');
        
        // 这里可以添加更复杂的逻辑来检查表单是否被修改
        // 简单实现：检查字段是否有值
        return (username && username.value.trim()) || (email && email.value.trim());
    }
});