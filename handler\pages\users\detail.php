<?php
/**
 * 用户详细信息页面
 * 
 * 展示单个用户的完整详细信息
 * 使用绿色布局
 */

// 引入必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;
use Core\OwnLibrary\Security\CsrfTokenManager;

// 布局变量
$layout="green_layout";

// 设置页面特定变量
$pageTitle = '用户详情 - AiPHP应用';
$pageDescription = '展示单个用户完整详细信息的页面';
$pageKeywords = '用户详情,用户信息,AiPHP';

// 引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/users/detail.css'];
$additionalJS = ['/static/own/js/users/detail.js'];

// 实例化CSRF令牌管理器
$csrfManager = new CsrfTokenManager();

// 生成CSRF令牌
$csrfToken = $csrfManager->getToken();

// 获取用户ID（从路由参数中提取）
$userId = isset($id) ? (int)$id : 0;

// 获取来源页面URL参数
$sourcePage = isset($_GET['sourcePage']) ? $_GET['sourcePage'] : '/users';

// 用户数据
$user = null;
$errorMessage = '';

try {
    // 查询用户详细信息
    $user = R::load('users', $userId);
    
    if (!$user->id) {
        $errorMessage = '未找到指定的用户';
    }
    
} catch (Exception $e) {
    // 如果出现错误，设置错误消息
    $errorMessage = '数据库操作失败: ' . $e->getMessage();
}

// 添加CSRF令牌到页面变量，以便在布局中使用
$csrfTokenMeta = '<meta name="csrf-token" content="' . htmlspecialchars($csrfToken) . '">';
// 赋值给布局文件使用的additionalMeta变量
$additionalMeta = $csrfTokenMeta;
?>
<div class="user-detail-page">
    <div class="page-header">
        <h1>用户详情</h1>
        <a href="<?php echo htmlspecialchars($sourcePage); ?>" class="back-button">返回用户列表</a>
    </div>
    
    <?php if ($errorMessage): ?>
        <div class="error-message"><?php echo htmlspecialchars($errorMessage); ?></div>
    <?php elseif ($user && $user->id): ?>
    <div class="user-detail-container">
        <div class="user-info-section">
            <h2>基本信息</h2>
            <table class="user-info-table">
                <tbody>
                    <tr>
                        <td class="label">用户ID:</td>
                        <td class="value"><?php echo htmlspecialchars($user->id); ?></td>
                    </tr>
                    <tr>
                        <td class="label">用户名:</td>
                        <td class="value"><?php echo htmlspecialchars($user->username); ?></td>
                    </tr>
                    <tr>
                        <td class="label">昵称:</td>
                        <td class="value"><?php echo htmlspecialchars($user->nickname); ?></td>
                    </tr>
                    <tr>
                        <td class="label">邮箱:</td>
                        <td class="value"><?php echo htmlspecialchars($user->email); ?></td>
                    </tr>
                    <tr>
                        <td class="label">性别:</td>
                        <td class="value"><?php echo $user->gender == 1 ? '男' : '女'; ?></td>
                    </tr>
                    <tr>
                        <td class="label">年龄:</td>
                        <td class="value"><?php echo htmlspecialchars($user->age); ?></td>
                    </tr>
                    <tr>
                        <td class="label">状态:</td>
                        <td class="value">
                            <span class="status-badge <?php echo $user->status == 1 ? 'active' : 'inactive'; ?>">
                                <?php echo $user->status == 1 ? '活跃' : '禁用'; ?>
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="user-timeline-section">
            <h2>时间信息</h2>
            <table class="user-info-table">
                <tbody>
                    <tr>
                        <td class="label">创建时间:</td>
                        <td class="value"><?php echo htmlspecialchars($user->created_at); ?></td>
                    </tr>
                    <tr>
                        <td class="label">更新时间:</td>
                        <td class="value"><?php echo htmlspecialchars($user->updated_at ?? '未更新'); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        // 删除了编辑和删除按钮
    </div>
    <?php endif; ?>
</div>