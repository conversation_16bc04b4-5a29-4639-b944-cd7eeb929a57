<?php
/**
 * RedBeanPHP类库测试文件
 * 
 * 用于测试RedBeanPHP类库的功能
 */

/**
 * 测试引导部分
 * 
 * 专门用于测试环境的引导代码，包含自动加载配置
 */

// 定义所有必需的路径常量
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('HANDLER_PATH', ROOT_PATH . '/handler');
define('PAGES_PATH', HANDLER_PATH . '/pages');
define('LAYOUTS_PATH', HANDLER_PATH . '/layouts');
define('OWN_LIBRARY_PATH', CORE_PATH . '/own-library');
define('OTHER_LIBRARY_PATH', CORE_PATH . '/other-library');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STATIC_PATH', PUBLIC_PATH . '/static');

// 注册自动加载函数
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
// 更新命名空间映射以匹配实际目录结构
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

/**
 * 具体测试代码部分
 * 
 * 用于测试RedBeanPHP类库的功能
 */

// 使用自动加载引入RedBeanPHP类和门面类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

echo "RedBeanPHP类库测试...\n";

try {
    // 1. 测试初始化
    echo "\n=== 测试初始化 ===\n";
    R::initialize();
    echo "RedBeanPHP初始化成功\n";
    
    // 2. 测试创建Bean
    echo "\n=== 测试创建Bean ===\n";
    $user = R::dispense('user');
    $user->username = 'testuser';
    $user->email = '<EMAIL>';
    $user->created_at = date('Y-m-d H:i:s');
    
    // 3. 测试保存Bean
    echo "\n=== 测试保存Bean ===\n";
    $id = R::store($user);
    echo "用户保存成功，ID: " . $id . "\n";
    
    // 4. 测试查找Bean
    echo "\n=== 测试查找Bean ===\n";
    $users = R::find('user');
    echo "找到 " . count($users) . " 个用户\n";
    
    // 5. 测试查找单个Bean
    echo "\n=== 测试查找单个Bean ===\n";
    $foundUser = R::findOne('user', 'username = ?', ['testuser']);
    if ($foundUser) {
        echo "找到用户: " . $foundUser->username . " (" . $foundUser->email . ")\n";
    } else {
        echo "未找到用户\n";
    }
    
    // 6. 测试通过ID加载Bean
    echo "\n=== 测试通过ID加载Bean ===\n";
    $loadedUser = R::load('user', $id);
    if ($loadedUser->id) {
        echo "加载用户成功: " . $loadedUser->username . " (" . $loadedUser->email . ")\n";
    } else {
        echo "未找到ID为 " . $id . " 的用户\n";
    }
    
    // 7. 测试更新Bean
    echo "\n=== 测试更新Bean ===\n";
    $loadedUser->username = 'updateduser';
    $updatedId = R::store($loadedUser);
    echo "用户更新成功，ID: " . $updatedId . "\n";
    
    // 8. 测试删除Bean
    echo "\n=== 测试删除Bean ===\n";
    R::trash($loadedUser);
    echo "用户删除成功\n";
    
    // 9. 再次查找确认删除
    echo "\n=== 确认删除 ===\n";
    $deletedUser = R::findOne('user', 'id = ?', [$id]);
    if ($deletedUser) {
        echo "用户未被删除\n";
    } else {
        echo "用户已成功删除\n";
    }
    
    echo "\nRedBeanPHP类库测试完成。\n";
    
} catch (Exception $e) {
    echo "测试出错: " . $e->getMessage() . "\n";
    echo "错误跟踪: " . $e->getTraceAsString() . "\n";
}