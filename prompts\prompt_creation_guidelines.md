# AiPHP 提示词文件创建指南

## 概述
本指南详细说明了在AiPHP框架中创建提示词文件的规范和最佳实践，确保提示词文件的一致性、可维护性和可用性。

## 提示词文件命名规范
- 文件名格式：使用小写字母，单词间用下划线分隔，格式为`[功能描述]_guidelines.md`
- 例如：`test_guidelines.md`、`coding_guidelines.md`
- 文件名应清晰反映提示词的主要功能和用途

## 提示词文件存放位置
- 所有提示词文件必须放置在项目根目录下的`prompts/`文件夹中
- 提示词文件的路径格式为：`prompts/[功能描述]_guidelines.md`

## 提示词文件内容规范
- 内容应简洁明了，只包含与该功能相关的规范和要求
- 使用Markdown格式编写，确保格式清晰易读
- 标题层级分明，使用`#`、`##`、`###`等标记不同层级的标题
- 规范内容应具体、明确，避免模糊不清的描述
- 对于代码示例，应使用Markdown代码块格式（```语言标签）

## 提示词文件结构建议
提示词文件建议包含以下结构：

1. **概述**：简要说明该提示词的目的和用途
2. **核心规范**：列出该功能的主要规范要求
3. **详细规则**：详细说明各项具体规则
4. **示例**：提供符合规范的示例
5. **最佳实践**：列出相关的最佳实践建议

## 提示词文件更新维护规范
- 当项目规范发生变化时，应及时更新相应的提示词文件
- 更新内容应与实际项目需求保持一致
- 新增功能或规范时，应创建对应的提示词文件
- 定期审查现有提示词文件，确保其时效性和准确性

## 提示词文件使用规范
- 在执行相关任务前，应先查阅对应的提示词文件
- 严格按照提示词文件中的规范和要求执行任务
- 如有疑问或发现问题，应及时反馈并更新提示词文件