<?php
/**
 * 检查数据库中用户数量的脚本
 */

// 定义必要的常量
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CORE_PATH', ROOT_PATH . '/core');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// 引入自动加载器
require_once CORE_PATH . '/own-library/autoloader/autoloader.php';

// 注册自动加载器
use Core\OwnLibrary\Autoloader\Autoloader;

$autoloader = new Autoloader();
$autoloader->addNamespace('Core\OwnLibrary', CORE_PATH . '/own-library');
$autoloader->addNamespace('Core\OtherLibrary', CORE_PATH . '/other-library');
// 更新命名空间映射以匹配实际目录结构
$autoloader->addNamespace('Core\OtherLibrary\RedBean', CORE_PATH . '/other-library/redbeanphp');
$autoloader->register();

// 引入必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;

try {
    // 获取总记录数
    $totalUsers = R::count('users');
    echo "数据库中用户总数: " . $totalUsers . "\n";
    
    // 测试不同的OFFSET值
    $limit = 10;
    for ($offset = 0; $offset <= 50; $offset += 10) {
        echo "\n测试 OFFSET = $offset:\n";
        try {
            $users = R::find('users', ' ORDER BY id ASC LIMIT ? OFFSET ?', [$limit, $offset]);
            echo "  成功获取 " . count($users) . " 条记录\n";
            foreach ($users as $user) {
                echo "    ID: " . $user->id . ", 用户名: " . $user->username . "\n";
            }
        } catch (Exception $e) {
            echo "  错误: " . $e->getMessage() . "\n";
        }
    }
    
    // 特别测试OFFSET=40和OFFSET=50的情况
    echo "\n特别测试:\n";
    
    echo "OFFSET = 40:\n";
    try {
        $users = R::find('users', ' ORDER BY id ASC LIMIT ? OFFSET ?', [$limit, 40]);
        echo "  成功获取 " . count($users) . " 条记录\n";
    } catch (Exception $e) {
        echo "  错误: " . $e->getMessage() . "\n";
    }
    
    echo "OFFSET = 50:\n";
    try {
        $users = R::find('users', ' ORDER BY id ASC LIMIT ? OFFSET ?', [$limit, 50]);
        echo "  成功获取 " . count($users) . " 条记录\n";
    } catch (Exception $e) {
        echo "  错误: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "数据库操作失败: " . $e->getMessage() . "\n";
}
?>