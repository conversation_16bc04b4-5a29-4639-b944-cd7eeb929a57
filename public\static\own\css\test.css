/* 参数验证测试页面样式文件 */

/* 主内容区域 */
.test-page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

/* 验证结果区域 */
.validation-results {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.validation-result {
    padding: 0.8rem;
    margin-bottom: 0.8rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.validation-result:last-child {
    margin-bottom: 0;
}

.validation-result.success {
    background-color: #dcfce7;
    border: 1px solid #86efac;
}

.validation-result.error {
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
}

.success-message {
    color: #15803d;
    font-weight: 500;
    margin-left: 0.5rem;
}

.error-message {
    color: #dc2626;
    font-weight: 500;
    margin-left: 0.5rem;
}

/* 验证测试链接区域 */
.validation-tests {
    margin-top: 2rem;
}

.validation-category {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 1.2rem;
    margin-bottom: 1.5rem;
}

.validation-category .category-title {
    margin-top: 0;
    color: #1e40af;
    border-bottom: 1px solid #cbd5e1;
    padding-bottom: 0.5rem;
    font-size: 1.4rem;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.validation-category ul {
    list-style-type: none;
    padding-left: 1rem;
}

.validation-category li {
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.2rem;
}

.validation-category li::before {
    content: "→";
    position: absolute;
    left: 0;
    color: #40916c;
}

.validation-category a {
    color: #2563eb;
    text-decoration: none;
    transition: color 0.2s ease, text-decoration 0.2s ease;
}

.validation-category a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .test-page-content {
        padding: 1rem;
    }
    
    .test-page-content h1 {
        font-size: 1.8rem;
    }
}