<?php

/**
 * 路由配置文件
 * 定义应用程序的路由规则
 */

return [
    // 默认路由
    'default' => 'home',
    
    // 路由规则
    'routes' => [
        // GET请求路由
        'GET' => [
            '/' => 'home',
            '/about' => 'about',
            '/database-test' => 'database_test',
            '/database-example' => 'database_example',
            // 带参数的测试页面路由
            '/test' => 'test',
            '/test/{id}' => 'test',
            '/test/{id}/{slug}' => 'test',
            // 路由参数测试页面
            '/test_param' => 'test_param',
            '/test_param/{id}' => 'test_param',
            // 新闻页面路由
            '/news' => 'news',
            // CSRF保护测试页面
            '/csrf-test' => 'csrf_test',
            // 用户列表页面路由
            '/users' => 'users/index',
            // 用户详情页面路由
            '/users/detail/{id}' => 'users/detail',
            // 用户表单页面路由（添加/编辑）
            '/users/form' => 'users/form',
            '/users/form/{id}' => 'users/form',
            // RedBeanPHP用户管理路由
            '/rb_users' => 'rb_users/index',
            '/rb_users/detail/{id}' => 'rb_users/detail',
            '/rb_users/edit/{id}' => 'rb_users/form',
            '/rb_users/create' => 'rb_users/form',
        ],
        
        // POST请求路由
        'POST' => [
            '/csrf-test' => 'csrf_test',
            // 用户状态更新路由
            '/users/update-status' => 'users/update_user_status',
            // 用户表单提交路由
            '/users/save' => 'users/form',
            // 用户删除路由
            '/users/delete' => 'users/delete_user',
            // RedBeanPHP用户删除路由
            '/rb_users/delete/{id}' => 'rb_users/delete_user',
            // RedBeanPHP用户表单提交路由
            '/rb_users/save' => 'rb_users/form',
        ],
        
        // PUT请求路由
        'PUT' => [
        ],
        
        // DELETE请求路由
        'DELETE' => [
        ],
    ],
    
    // 404页面
    'not_found' => '404',
    
    // 启用调试模式
    'debug_mode' => true,
];