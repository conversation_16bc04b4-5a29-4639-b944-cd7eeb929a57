/* 用户表单页面样式 */

/* 全局样式重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 表单容器 */
.user-form-container {
    max-width: 600px;
    margin: 40px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 表单头部 */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.form-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

.back-link {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.back-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* 消息提示 */
.error-message,
.success-message {
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-size: 14px;
}

.error-message {
    background-color: #fee;
    color: #c0392b;
    border-left: 4px solid #e74c3c;
}

.success-message {
    background-color: #e6f7ee;
    color: #27ae60;
    border-left: 4px solid #2ecc71;
}

/* 表单样式 */
#user-form {
    display: flex;
    flex-direction: column;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="number"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.form-group input.error {
    border-color: #e74c3c;
}

.error-text {
    display: block;
    margin-top: 4px;
    color: #e74c3c;
    font-size: 12px;
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 6px;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
}

.radio-option input[type="radio"] {
    margin-right: 6px;
}

.radio-option span {
    color: #333;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.submit-btn {
    background-color: #3498db;
    color: white;
    flex: 1;
}

.submit-btn:hover {
    background-color: #2980b9;
}

.cancel-btn {
    background-color: #95a5a6;
    color: white;
    flex: 1;
}

.cancel-btn:hover {
    background-color: #7f8c8d;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-form-container {
        margin: 20px;
        padding: 15px;
    }
    
    .form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .submit-btn,
    .cancel-btn {
        width: 100%;
    }
}