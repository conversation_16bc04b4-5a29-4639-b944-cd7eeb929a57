/* 用户列表页面样式 */
.users-page {
    padding: 20px 0;
}

/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4caf50;
}

.page-header h1 {
    margin: 0;
    color: #2e7d32;
    font-size: 24px;
}

/* 添加用户按钮样式 */
.add-user-button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.add-user-button:hover {
    background-color: #388e3c;
}

.add-user-button:active {
    transform: translateY(1px);
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid #ef5350;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
}

.users-table th {
    background-color: #4caf50;
    color: white;
    text-align: left;
    padding: 12px 15px;
    font-weight: 600;
}

.users-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.users-table tr:nth-child(even) {
    background-color: #f5f5f5;
}

.users-table tr:hover {
    background-color: #f1f8e9;
}

/* 状态徽章样式 */
.status-cell {
    text-align: center;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.status-badge.clickable:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.status-badge.active {
    background-color: #c8e6c9;
    color: #2e7d32;
}

.status-badge.active.clickable:hover {
    background-color: #a5d6a7;
}

.status-badge.inactive {
    background-color: #ffcdd2;
    color: #c62828;
}

.status-badge.inactive.clickable:hover {
    background-color: #ef9a9a;
}

/* 加载状态 */
.status-badge.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 操作按钮样式 */
.action-cell {
    text-align: center;
    white-space: nowrap;
}

.view-btn,
.edit-btn,
.delete-btn {
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-right: 6px;
}

.view-btn {
    background-color: #2196f3;
    color: white;
}

.view-btn:hover {
    background-color: #0b7dda;
}

.edit-btn {
    background-color: #ff9800;
    color: white;
}

.edit-btn:hover {
    background-color: #e68900;
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #d32f2f;
}

/* 分页样式 */
.pagination {
    margin-top: 20px;
    text-align: center;
}

.pagination p {
    margin-bottom: 10px;
    color: #666;
}

.pagination-links {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.page-link {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    background-color: white;
    color: #4caf50;
    border: 1px solid #4caf50;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.page-link:hover {
    background-color: #4caf50;
    color: white;
}

.page-link.current {
    background-color: #4caf50;
    color: white;
    font-weight: 600;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .users-table {
        display: block;
        overflow-x: auto;
    }
    
    .pagination-links {
        flex-direction: column;
    }
    
    .page-link {
        width: 100%;
        max-width: 200px;
        text-align: center;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.modal.modal-open {
    opacity: 1;
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    overflow: visible !important;
}

.modal.modal-open .modal-content {
    transform: translateY(0);
}

.modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 10;
}

.modal-close:hover {
    background-color: #f0f0f0;
}

.modal-iframe-container {
    width: 100%;
    height: auto;
    min-height: 500px;
    padding: 10px;
    overflow: visible !important;
}

.modal-iframe {
    width: 100%;
    height: auto;
    border: none;
    min-height: 500px;
}

/* 删除确认模态框样式 */
.delete-modal {
    width: 90%;
    max-width: 400px;
    padding: 20px;
    text-align: center;
}

.delete-modal h3 {
    margin-top: 0;
    color: #f44336;
}

.delete-modal p {
    margin: 20px 0;
    font-size: 16px;
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-secondary {
    background-color: #9e9e9e;
    color: white;
}

.btn-secondary:hover {
    background-color: #757575;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

/* 消息通知模态框样式 */
.notification-modal {
    width: 90%;
    max-width: 300px;
    padding: 20px;
    text-align: center;
    background-color: #4caf50;
    color: white;
}

.notification-modal.error {
    background-color: #f44336;
}

@media (max-width: 768px) {
    .modal-content {
        width: 95% !important;
        max-height: 95vh !important;
        height: auto !important;
    }
    
    .modal-iframe-container {
        min-height: 400px;
    }
    
    .modal-iframe {
        min-height: 400px;
    }
    
    .delete-modal {
        padding: 15px;
    }
}