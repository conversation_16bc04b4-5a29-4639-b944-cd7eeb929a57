/**
 * 数据库测试页面样式
 * 
 * 用于美化数据库测试页面的显示效果
 */

.database-test-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
}

.database-test-page h1 {
    color: #27ae60;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

.database-test-page h2 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
}

.database-test-page p {
    font-size: 1.1em;
    line-height: 1.6;
    color: #333;
    margin-bottom: 15px;
}

.user-data {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* 用户数据表格样式 */
.user-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.user-table th,
.user-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.user-table th {
    background-color: #27ae60;
    color: white;
    font-weight: bold;
}

.user-table tr:hover {
    background-color: #f5f5f5;
}

.user-table tr:last-child td {
    border-bottom: none;
}

/* 无数据提示样式 */
.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
}

/* 分页样式 */
.pagination {
    margin-top: 30px;
    text-align: center;
}

.pagination p {
    margin-bottom: 15px;
    font-weight: bold;
}

.pagination-links {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 5px;
}

.pagination-links a,
.pagination-links span {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s;
}

.pagination-links a {
    background-color: #e9ecef;
    color: #495057;
    border: 1px solid #dee2e6;
}

.pagination-links a:hover {
    background-color: #27ae60;
    color: white;
    border-color: #27ae60;
}

.pagination-links .current {
    background-color: #27ae60;
    color: white;
    font-weight: bold;
    border: 1px solid #27ae60;
}

.page-info {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .database-test-page h1 {
        font-size: 2em;
    }
    
    .database-test-page {
        padding: 10px;
    }
    
    .user-data, .page-info {
        padding: 15px;
    }
    
    .user-table {
        font-size: 0.9em;
    }
    
    .user-table th,
    .user-table td {
        padding: 8px 10px;
    }
    
    .pagination-links {
        gap: 2px;
    }
    
    .pagination-links a,
    .pagination-links span {
        padding: 6px 8px;
        font-size: 0.9em;
    }
}