<?php
/**
 * 用户列表页面
 * 
 * 展示用户列表并提供分页功能，每页显示10条记录
 * 使用绿色布局
 */

// 引入必要的类
use Core\OtherLibrary\RedBean\RedBeanFacade as R;
// use Core\OwnLibrary\Security\CsrfTokenManager; // 暂时注释掉CSRF管理器

// 布局变量
$layout="green_layout";

// 设置页面特定变量
$pageTitle = '用户列表 - AiPHP应用';
$pageDescription = '展示所有用户信息的列表页面';
$pageKeywords = '用户列表,用户管理,AiPHP';

// 引入对应的CSS和JS文件
$additionalCSS = ['/static/own/css/users/index.css'];
$additionalJS = ['/static/own/js/users/index.js'];

// 实例化CSRF令牌管理器
// $csrfManager = new CsrfTokenManager(); // 暂时注释掉CSRF管理器实例化

// 生成CSRF令牌
// $csrfToken = $csrfManager->getToken(); // 暂时注释掉CSRF令牌生成
$csrfToken = ''; // 临时设置为空字符串

// 获取分页参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$page = max(1, $page); // 确保页码至少为1
$limit = 10; // 每页显示10条记录

// 用户数据和分页信息
$users = [];
$totalUsers = 0;
$totalPages = 1;
$errorMessage = ''; // 初始化错误消息变量

try {
    // 获取总记录数
    $totalUsers = R::count('users');
    $totalPages = max(1, ceil($totalUsers / $limit)); // 确保至少有1页

    // 确保页码在有效范围内
    $page = max(1, min($page, $totalPages));

    // 标准分页计算：OFFSET = (页码 - 1) * 每页记录数
    $offset = ($page - 1) * $limit;
    // 注意：不要在这里添加任何echo语句，会导致500错误

    // 只有当有用户数据时才查询
    if ($totalUsers > 0) {
        // 查询当前页的用户数据，使用RedBeanPHP支持的分页方式
        // RedBeanPHP不支持在SQL中使用参数绑定的LIMIT和OFFSET，需要直接拼接
        $users = R::find('users', " ORDER BY id ASC LIMIT {$limit} OFFSET {$offset}");
    } else {
        $users = [];
    }
    
} catch (Exception $e) {
    // 如果出现错误，设置错误消息
    $errorMessage = '数据库操作失败: ' . $e->getMessage() . ' 在文件 ' . $e->getFile() . ' 第 ' . $e->getLine() . ' 行';
    error_log("用户列表页面错误: " . $errorMessage); // 记录错误日志
    $users = [];
} catch (Error $e) {
    // 捕获致命错误
    $errorMessage = '数据库操作失败 (致命错误): ' . $e->getMessage() . ' 在文件 ' . $e->getFile() . ' 第 ' . $e->getLine() . ' 行';
    error_log("用户列表页面致命错误: " . $errorMessage); // 记录错误日志
    $users = [];
}

// 添加CSRF令牌到页面变量，以便在布局中使用
// $csrfTokenMeta = '<meta name="csrf-token" content="' . htmlspecialchars($csrfToken) . '">'; // 暂时注释掉
$csrfTokenMeta = ''; // 临时设置为空字符串
// 赋值给布局文件使用的additionalMeta变量
$additionalMeta = $csrfTokenMeta;
?>
<div class="users-page">
    <div class="page-header">
        <h1>用户列表</h1>
        <button id="add-user-btn" class="add-user-button">添加用户</button>
    </div>
    
    <?php if (isset($errorMessage)): ?>
        <div class="error-message"><?php echo htmlspecialchars($errorMessage); ?></div>
    <?php endif; ?>
    
    <?php if (!empty($users)): ?>
    <table class="users-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>昵称</th>
                <th>邮箱</th>
                <th>性别</th>
                <th>年龄</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $user): ?>
            <tr>
                <td><?php echo htmlspecialchars($user->id); ?></td>
                <td><?php echo htmlspecialchars($user->username); ?></td>
                <td><?php echo htmlspecialchars($user->nickname); ?></td>
                <td><?php echo htmlspecialchars($user->email); ?></td>
                <td><?php echo $user->gender == 1 ? '男' : '女'; ?></td>
                <td><?php echo htmlspecialchars($user->age); ?></td>
                <td class="status-cell">
                    <span class="status-badge clickable <?php echo $user->status == 1 ? 'active' : 'inactive'; ?>" data-id="<?php echo $user->id; ?>" data-status="<?php echo $user->status; ?>">
                        <?php echo $user->status == 1 ? '活跃' : '禁用'; ?>
                    </span>
                </td>
                <td><?php echo htmlspecialchars($user->created_at); ?></td>
                <td class="action-cell">
                    <button class="view-btn" data-id="<?php echo $user->id; ?>">查看</button>
                    <button class="edit-btn" data-id="<?php echo $user->id; ?>">编辑</button>
                    <button class="delete-btn" data-id="<?php echo $user->id; ?>">删除</button>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <!-- 分页导航 -->
    <div class="pagination">
        <p>总共 <?php echo $totalUsers; ?> 条记录，共 <?php echo $totalPages; ?> 页，当前第 <?php echo $page; ?> 页</p>
        <div class="pagination-links">
            <?php if ($page > 1): ?>
                <a href="?page=1" class="page-link">&laquo; 首页</a>
                <a href="?page=<?php echo $page - 1; ?>" class="page-link">&lt; 上一页</a>
            <?php endif; ?>
            
            <?php
            // 显示页码链接
            $startPage = max(1, $page - 2);
            $endPage = min($totalPages, $page + 2);
            
            for ($i = $startPage; $i <= $endPage; $i++):
            ?>
                <?php if ($i == $page): ?>
                    <span class="page-link current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?>" class="page-link"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
                <a href="?page=<?php echo $page + 1; ?>" class="page-link">下一页 &gt;</a>
                <a href="?page=<?php echo $totalPages; ?>" class="page-link">末页 &raquo;</a>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>
    <p class="no-data">暂无用户数据</p>
    <?php endif; ?>
</div>

<!-- 删除确认模态框 -->
<div id="delete-confirm-modal" class="modal">
    <div class="modal-content delete-modal">
        <span class="modal-close">&times;</span>
        <h3>确认删除</h3>
        <p>确定要删除用户 "<span id="delete-username"></span>" 吗？此操作不可恢复！</p>
        <div class="modal-actions">
            <button id="cancel-delete" class="btn btn-secondary">取消</button>
            <button id="confirm-delete" class="btn btn-danger">确定</button>
        </div>
    </div>
</div>

<!-- 消息通知模态框 -->
<div id="message-notification" class="modal">
    <div class="modal-content notification-modal">
        <p id="notification-message"></p>
    </div>
</div>