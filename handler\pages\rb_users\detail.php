<?php
/**
 * 使用RedBeanPHP的用户详情页面
 */
// 布局变量
$layout="green_layout";
// 设置页面标题和描述
$pageTitle = '用户详情 - RedBeanPHP版本';
$pageDescription = '使用RedBeanPHP ORM的用户详情页面';
$pageKeywords = '用户详情, RedBeanPHP, ORM';

// 添加页面特定的CSS文件和JS文件
$additionalCSS = ['/static/own/css/rb_users/detail.css'];
$additionalJS = ['/static/own/js/rb_users/detail.js'];

// 获取用户ID
$userId = $_GET['id'] ?? null;

if (empty($userId)) {
    header('Location: /rb_users');
    exit;
}

try {
    // 获取用户信息（从users表而不是user表）
    $user = R::load('users', $userId);
    
    if (!$user->id) {
        $error = "用户不存在";
    }
} catch (Exception $e) {
    $error = "获取用户信息失败: " . $e->getMessage();
    $user = null;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">用户详情</h3>
                    <div class="card-tools">
                        <a href="/rb_users" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="/rb_users/edit/<?php echo $userId; ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php elseif ($user): ?>
                        <table class="table table-bordered">
                            <tr>
                                <th>ID</th>
                                <td><?php echo htmlspecialchars($user->id); ?></td>
                            </tr>
                            <tr>
                                <th>用户名</th>
                                <td><?php echo htmlspecialchars($user->username); ?></td>
                            </tr>
                            <tr>
                                <th>邮箱</th>
                                <td><?php echo htmlspecialchars($user->email); ?></td>
                            </tr>
                            <tr>
                                <th>创建时间</th>
                                <td><?php echo htmlspecialchars($user->created_at); ?></td>
                            </tr>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>